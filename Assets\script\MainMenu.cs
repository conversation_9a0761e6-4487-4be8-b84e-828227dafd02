/*
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 * ███╗   ███╗ █████╗ ██╗███╗   ██╗    ███╗   ███╗███████╗███╗   ██╗██╗   ██╗    ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗
 * ████╗ ████║██╔══██╗██║████╗  ██║    ████╗ ████║██╔════╝████╗  ██║██║   ██║    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
 * ██╔████╔██║███████║██║██╔██╗ ██║    ██╔████╔██║█████╗  ██╔██╗ ██║██║   ██║    ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
 * ██║╚██╔╝██║██╔══██║██║██║╚██╗██║    ██║╚██╔╝██║██╔══╝  ██║╚██╗██║██║   ██║    ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
 * ██║ ╚═╝ ██║██║  ██║██║██║ ╚████║    ██║ ╚═╝ ██║███████╗██║ ╚████║╚██████╔╝    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
 * ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝    ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝     ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * 🎮 TRACTOR SIMULATOR CARGO GAMES - MAIN MENU MANAGER 🏠
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📋 SCRIPT INFORMATION                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  📁 File Name      : MainMenu.cs                                                                                   │
 * │  🎯 Purpose        : Main Menu System & Level Management Hub                                                       │
 * │  🏗️  Architecture   : Comprehensive Menu System with Multi-Mode Level Management                                   │
 * │  🎨 Script Organizer: Ali Taj                                                                                       │
 * │  📅 Organized Date : 2025-07-30                                                                                     │
 * │  ⚡ Version        : V1.9 - Optimized & Beautifully Organized                                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  🌟 KEY FEATURES                                                                                                    │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🎮 Multi-Mode Level System   - Career, Farm, and Touchan level management                                        │
 * │  💾 Progress Persistence      - PlayerPrefs-based save system for all game modes                                  │
 * │  🔓 Dynamic Level Unlocking   - Sequential level progression with visual state management                          │
 * │  🎵 Audio Management          - Volume controls and sound effect integration                                       │
 * │  💰 Currency System           - Coin display and tracking                                                          │
 * │  🎨 Visual State Management   - Three-state level buttons (locked, available, completed)                          │
 * │  🔄 Scene Navigation          - Smooth transitions between different game modes                                     │
 * │  📱 Loading Screen Integration- Professional loading panels for scene transitions                                  │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📚 CORE FUNCTIONALITY                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🔹 Level Management Hub      - Central control for all three game modes                                           │
 * │  🔹 Progress Tracking         - Comprehensive save/load system using PlayerPrefs                                   │
 * │  🔹 Visual State System       - Dynamic button states with child object management                                 │
 * │  🔹 Audio Control Center      - Volume sliders and sound effect management                                         │
 * │  🔹 Scene Loading Manager     - Professional loading screens and transitions                                       │
 * │  🔹 Level Completion Handler  - Automatic unlocking and progress advancement                                       │
 * │  🔹 UI Navigation System      - Smooth menu transitions and button interactions                                    │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  💡 USAGE INSTRUCTIONS                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  1️⃣  Attach this script to the Main Menu GameObject                                                               │
 * │  2️⃣  Configure level button arrays for Career, Farm, and Touchan modes                                           │
 * │  3️⃣  Set up audio sources and volume sliders                                                                      │
 * │  4️⃣  Assign UI elements (coins text, loading panel)                                                               │
 * │  5️⃣  Ensure each level button has 3 child objects (complete, locked, selected)                                   │
 * │  6️⃣  Test level progression and visual state changes                                                              │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎨 Beautifully Organized by Ali Taj - Making Code Art! ✨
 *
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class MainMenu : MonoBehaviour
{
    #region Level Management
    public GameObject[] CareerLevelButtons;
    public GameObject[] FarmLevelButtons;
    public GameObject[] TouchanLevelButtons;
    public static int levlno;
    #endregion

    #region Audio System
    public AudioSource BtnSound;
    public AudioSource Sound;
    public Slider music;
    public Slider sound;
    #endregion

    #region UI Elements
    public Text coins;
    public GameObject lodingpanl;
    #endregion
    #region Unity Lifecycle
    void Start()
    {
        InitializeGame();
        InitializeAudio();
        InitializeLevels();
    }
    #endregion

    #region Initialization Methods
    private void InitializeGame()
    {
        Time.timeScale = 1;
        coins.text = PlayerPrefs.GetInt("coins").ToString();
    }

    private void InitializeAudio()
    {
        music.value = PlayerPrefs.GetFloat("MusicVolume", 0.5f);
        sound.value = PlayerPrefs.GetFloat("SoundVolume", 0.5f);

        BtnSound.volume = music.value;
        Sound.volume = sound.value;
        music.onValueChanged.AddListener(OnMusicVolumeChanged);
        sound.onValueChanged.AddListener(OnSoundVolumeChanged);
    }

    private void InitializeLevels()
    {
        InitializeCareerLevels();
        InitializeFarmLevels();
        InitializeTouchanLevels();
    }
    private void InitializeCareerLevels()
    {
        for (int i = 0; i < CareerLevelButtons.Length; i++)
        {
            // Get level completion status from PlayerPrefs
            int levelStatus = PlayerPrefs.GetInt("Career" + i, 0); // 0 = locked, 1 = available, 2 = completed

            // Get child objects for different states
            Transform completeChild = CareerLevelButtons[i].transform.GetChild(0); // Complete state
            Transform lockChild = CareerLevelButtons[i].transform.GetChild(1);     // Lock state
            Transform selectedChild = CareerLevelButtons[i].transform.GetChild(2); // Selected/Available state

            // Reset all child states
            completeChild.gameObject.SetActive(false);
            lockChild.gameObject.SetActive(false);
            selectedChild.gameObject.SetActive(false);

            // Set appropriate state based on level status
            if (levelStatus == 2) // Level completed
            {
                completeChild.gameObject.SetActive(true);
                CareerLevelButtons[i].GetComponent<Button>().interactable = true;
            }
            else if (levelStatus == 1 || i == 0) // Level available or first level (always available)
            {
                selectedChild.gameObject.SetActive(true);
                CareerLevelButtons[i].GetComponent<Button>().interactable = true;

                // Unlock next level if current level is completed
                if (i > 0 && PlayerPrefs.GetInt("Career" + (i-1), 0) == 2)
                {
                    PlayerPrefs.SetInt("Career" + i, 1);
                }
            }
            else // Level locked
            {
                lockChild.gameObject.SetActive(true);
                CareerLevelButtons[i].GetComponent<Button>().interactable = false;
            }
        }
    }

    private void InitializeFarmLevels()
    {
        for (int i = 0; i < FarmLevelButtons.Length; i++)
        {
            // Get level completion status from PlayerPrefs
            int levelStatus = PlayerPrefs.GetInt("Farm" + i, 0); // 0 = locked, 1 = available, 2 = completed

            // Get child objects for different states
            Transform completeChild = FarmLevelButtons[i].transform.GetChild(0); // Complete state
            Transform lockChild = FarmLevelButtons[i].transform.GetChild(1);     // Lock state
            Transform selectedChild = FarmLevelButtons[i].transform.GetChild(2); // Selected/Available state

            // Reset all child states
            completeChild.gameObject.SetActive(false);
            lockChild.gameObject.SetActive(false);
            selectedChild.gameObject.SetActive(false);

            // Set appropriate state based on level status
            if (levelStatus == 2) // Level completed
            {
                completeChild.gameObject.SetActive(true);
                FarmLevelButtons[i].GetComponent<Button>().interactable = true;
            }
            else if (levelStatus == 1 || i == 0) // Level available or first level (always available)
            {
                selectedChild.gameObject.SetActive(true);
                FarmLevelButtons[i].GetComponent<Button>().interactable = true;

                // Unlock next level if current level is completed
                if (i > 0 && PlayerPrefs.GetInt("Farm" + (i-1), 0) == 2)
                {
                    PlayerPrefs.SetInt("Farm" + i, 1);
                }
            }
            else // Level locked
            {
                lockChild.gameObject.SetActive(true);
                FarmLevelButtons[i].GetComponent<Button>().interactable = false;
            }
        }
    }

    private void InitializeTouchanLevels()
    {
        for (int i = 0; i < TouchanLevelButtons.Length; i++)
        {
            // Get level completion status from PlayerPrefs
            int levelStatus = PlayerPrefs.GetInt("Touchan" + i, 0); // 0 = locked, 1 = available, 2 = completed

            // Get child objects for different states
            Transform completeChild = TouchanLevelButtons[i].transform.GetChild(0); // Complete state
            Transform lockChild = TouchanLevelButtons[i].transform.GetChild(1);     // Lock state
            Transform selectedChild = TouchanLevelButtons[i].transform.GetChild(2); // Selected/Available state

            // Reset all child states
            completeChild.gameObject.SetActive(false);
            lockChild.gameObject.SetActive(false);
            selectedChild.gameObject.SetActive(false);

            // Set appropriate state based on level status
            if (levelStatus == 2) // Level completed
            {
                completeChild.gameObject.SetActive(true);
                TouchanLevelButtons[i].GetComponent<Button>().interactable = true;
            }
            else if (levelStatus == 1 || i == 0) // Level available or first level (always available)
            {
                selectedChild.gameObject.SetActive(true);
                TouchanLevelButtons[i].GetComponent<Button>().interactable = true;

                // Unlock next level if current level is completed
                if (i > 0 && PlayerPrefs.GetInt("Touchan" + (i-1), 0) == 2)
                {
                    PlayerPrefs.SetInt("Touchan" + i, 1);
                }
            }
            else // Level locked
            {
                lockChild.gameObject.SetActive(true);
                TouchanLevelButtons[i].GetComponent<Button>().interactable = false;
            }
        }
    }
    #endregion

    #region Level Completion Methods
    /// <summary>
    /// Call this method when a Career level is completed
    /// </summary>
    /// <param name="levelIndex">Index of the completed level</param>
    public static void CompleteCareerLevel(int levelIndex)
    {
        PlayerPrefs.SetInt("Career" + levelIndex, 2); // Mark as completed

        // Unlock next level if it exists
        if (levelIndex + 1 < 10) // Assuming max 10 levels, adjust as needed
        {
            if (PlayerPrefs.GetInt("Career" + (levelIndex + 1), 0) == 0)
            {
                PlayerPrefs.SetInt("Career" + (levelIndex + 1), 1); // Unlock next level
            }
        }
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Call this method when a Farm level is completed
    /// </summary>
    /// <param name="levelIndex">Index of the completed level</param>
    public static void CompleteFarmLevel(int levelIndex)
    {
        PlayerPrefs.SetInt("Farm" + levelIndex, 2); // Mark as completed

        // Unlock next level if it exists
        if (levelIndex + 1 < 10) // Assuming max 10 levels, adjust as needed
        {
            if (PlayerPrefs.GetInt("Farm" + (levelIndex + 1), 0) == 0)
            {
                PlayerPrefs.SetInt("Farm" + (levelIndex + 1), 1); // Unlock next level
            }
        }
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Call this method when a Touchan level is completed
    /// </summary>
    /// <param name="levelIndex">Index of the completed level</param>
    public static void CompleteTouchanLevel(int levelIndex)
    {
        PlayerPrefs.SetInt("Touchan" + levelIndex, 2); // Mark as completed

        // Unlock next level if it exists
        if (levelIndex + 1 < 10) // Assuming max 10 levels, adjust as needed
        {
            if (PlayerPrefs.GetInt("Touchan" + (levelIndex + 1), 0) == 0)
            {
                PlayerPrefs.SetInt("Touchan" + (levelIndex + 1), 1); // Unlock next level
            }
        }
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Reset all level progress (for testing purposes)
    /// </summary>
    public static void ResetAllLevels()
    {
        // Reset Career levels
        for (int i = 0; i < 10; i++)
        {
            PlayerPrefs.DeleteKey("Career" + i);
        }

        // Reset Farm levels
        for (int i = 0; i < 10; i++)
        {
            PlayerPrefs.DeleteKey("Farm" + i);
        }

        // Reset Touchan levels
        for (int i = 0; i < 10; i++)
        {
            PlayerPrefs.DeleteKey("Touchan" + i);
        }

        PlayerPrefs.Save();
    }
    #endregion

    #region UI Navigation Methods
    public void yes()
    {
        Application.Quit();
    }

    #endregion
    #region Audio Settings Methods
    public void save()
    {

        PlayerPrefs.SetFloat("MusicVolume", music.value);
        PlayerPrefs.SetFloat("SoundVolume", sound.value);
        PlayerPrefs.Save();
    }

    public void OnMusicVolumeChanged(float value)
    {
        BtnSound.volume = value;
        PlayerPrefs.SetFloat("MusicVolume", value);
    }

    public void OnSoundVolumeChanged(float value)
    {
        Sound.volume = value;
        PlayerPrefs.SetFloat("SoundVolume", value);
    }
    #endregion
    #region Level Selection Methods
    public void Careerlevel(int Clevel)
    {
        levlno = Clevel;
        lodingpanl.SetActive(true);
        StartCoroutine(GamePlayC());

    }

    public void Farmlevel(int Flevel)
    {
        levlno = Flevel;
        lodingpanl.SetActive(true);
        StartCoroutine(FarmingC());

    }

    public void Touchanlevel(int Tlevel)
    {
        levlno = Tlevel;
        lodingpanl.SetActive(true);
        StartCoroutine(TouchanC());
    }
    #endregion

    #region Control Settings Methods
    public void Steer()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }

    public void Btns()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }

    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    #endregion
    #region Coroutines
    IEnumerator FarmingC()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("Farming mod");
    }

    IEnumerator GamePlayC()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("gameplay");
    }

    IEnumerator TouchanC()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("tractortochan");
    }
    #endregion

    #region External Links Methods
    public void rateus()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.tractor.trolly.games.farming.game");
    }

    public void privacy()
    {
        Application.OpenURL("https://simulatorgames2022.blogspot.com/2023/04/privacy-policy.html");
    }

    public void moregams()
    {
        Application.OpenURL("https://play.google.com/store/apps/dev?id=6151632225219809775");
    }

    public void mudjeep()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.offroadjeep.mudjeep.offtheroad.jeepgame.simulator.offroad.driving.games");
    }

    public void trainadd()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.city.train.simulator.zt.game&hl=en");
    }
    #endregion

}
