using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public enum AreaSize
{
    Area_20x20,
    Area_50x50,
    Area_100x100,
    Custom
}

[System.Serializable]
public class SmoothTrailPoint
{
    public Vector3 position;
    public Vector3 direction;
    public float width;
    public float intensity;
    public float timestamp;
    public float turnAngle;

    public SmoothTrailPoint(Vector3 pos, Vector3 dir, float w = 2f, float i = 1f, float angle = 0f)
    {
        position = pos;
        direction = dir.normalized;
        width = w;
        intensity = i;
        timestamp = Time.time;
        turnAngle = angle;
    }
}

public class TerrainPainter : MonoBehaviour
{

    public float paintRadius = 2f;
    public int targetLayerIndex = 0;     // Texture layer to check before painting (e.g. grass)
    public int applyLayerIndex = 1;      // Texture layer to apply (e.g. soil)
    public float minSpeed = 0.1f;        // Minimum speed to start painting
    public float maxSpeed = 10f;         // Maximum speed for painting calculations
    public float paintIntensity = 1f;    // How strong the painting effect is
    public bool useSquareBrush = true;   // Use square brush instead of circular
    public Vector3 paintOffset = Vector3.zero; // Manual offset for paint position
    public bool showDebugGizmos = false; // Show debug gizmos for paint position
    public float paintFrequency = 0.1f;  // How often to paint (lower = more frequent)

    [Header("Farming Simulator 2025 Speed Control")]
    public bool useSpeedBasedPainting = true;    // Enable speed-based painting like FS25
    public float slowPaintingSpeed = 2f;         // Speed for slow/detailed painting
    public float fastPaintingSpeed = 8f;         // Speed for fast painting
    public AnimationCurve speedPaintingCurve = AnimationCurve.EaseInOut(0, 0.3f, 1, 1f); // Speed to frequency curve
    public float minPaintFrequency = 0.05f;      // Fastest painting (high detail)
    public float maxPaintFrequency = 0.3f;       // Slowest painting (low detail)
    public bool showSpeedIndicator = true;       // Show current painting speed mode

    [Header("Smooth Turn Spline System")]
    public bool useSmoothTurnSpline = true;  // Enable smooth turn spline system like Farming Simulator 2025
    public float splineResolution = 0.3f;    // Resolution for spline points
    public float smoothingFactor = 0.4f;     // How much smoothing to apply (0-1)
    public int smoothingIterations = 2;      // Number of smoothing passes
    public float turnDetectionAngle = 10f;   // Minimum angle to detect a turn
    public float maxTrailLength = 50f;       // Maximum number of trail points to keep
    public bool autoCleanOldTrail = true;    // Automatically remove old trail points
    public float trailLifetime = 20f;        // How long trail points last

    [Header("Turn-Based Texture Blending")]
    public bool adaptiveWidthOnTurns = true; // Adjust brush width based on turn angle
    public float minTurnWidth = 1.5f;        // Minimum brush width during turns
    public float maxTurnWidth = 4f;          // Maximum brush width during sharp turns
    public float maxTurnAngle = 45f;         // Maximum turn angle for width calculation
    public AnimationCurve turnWidthCurve = AnimationCurve.EaseInOut(0, 0, 1, 1); // Curve for turn width
    public AnimationCurve turnIntensityCurve = AnimationCurve.Linear(0, 1, 1, 1); // Curve for turn intensity

    [Header("Seamless Texture Transition")]
    public bool useSeamlessTransition = true; // Enable seamless texture blending
    public float transitionSmoothness = 0.8f; // How smooth the texture transition is
    public float edgeFeathering = 1.2f;       // Feathering at brush edges for seamless blending

    [Header("Advanced Curve Smoothing - FS25 Style")]
    public bool useAdvancedCurveSmoothing = true;  // Ultra-smooth curves like FS25
    public float curvePreviewDistance = 5f;        // How far ahead to preview curves
    public float curvePredictionStrength = 0.7f;   // How much to predict curve direction
    public int advancedSmoothingPasses = 4;        // Extra smoothing passes for curves
    public float cornerSmoothingRadius = 3f;       // Radius for corner smoothing
    public bool adaptiveSplineResolution = true;   // Adjust resolution based on curve complexity

    [Header("Area Completion Settings")]
    public AreaSize areaSize = AreaSize.Area_50x50;
    public Vector2 customAreaSize = new Vector2(50, 50);
    public GameObject completionPanel; // Panel to show when area is complete
    public Text percentageText; // UI Text to show current percentage
    public Slider progressSlider; // UI Slider to show progress visually
    public float targetCompletionPercentage = 1000f; // Target percentage for completion

    [Header("Auto Complete Feature")]
    [Tooltip("When completion panel is shown, automatically paint entire target layer area")]
    public bool autoCompleteAreaOnFinish = true; // Automatically paint entire area when completion panel is shown

    [Header("Area Progress")]
    public float currentCompletionPercentage = 0f;
    public bool areaCompleted = false;

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Vector3 lastPosition;
    private float currentSpeed;
    private Vector3 velocity;
    private Renderer objectRenderer;
    private float lastPaintTime;

    // Area tracking variables
    private Vector2 currentAreaSize;
    private int totalAreaPixels;
    private int paintedPixels;

    // Smooth Turn Spline System variables
    private List<SmoothTrailPoint> trailPoints = new List<SmoothTrailPoint>();
    private List<Vector3> smoothedSpline = new List<Vector3>();
    private float lastSplineTime;
    private Vector3 lastDirection;
    private bool isInTurn = false;
    private float currentTurnAngle = 0f;

    // Speed-based painting variables
    private float currentPaintFrequency;
    private string currentSpeedMode = "Normal";
    private float adaptiveResolution;
    private List<Vector3> curvePreviewPoints = new List<Vector3>();

    // Public properties for external access (e.g., UI, debugging)
    public List<SmoothTrailPoint> TrailPoints => trailPoints;
    public List<Vector3> SmoothedSpline => smoothedSpline;
    public bool IsInTurn => isInTurn;
    public float CurrentTurnAngle => currentTurnAngle;
    public float CurrentSpeed => currentSpeed;
    public string CurrentSpeedMode => currentSpeedMode;
    public float CurrentPaintFrequency => currentPaintFrequency;
    [Header("UI")]
    public GameObject canvas, tractorcam, machinecam;

    void Start()
    {
        terrain = Terrain.activeTerrain;
        objectRenderer = GetComponent<Renderer>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        lastPosition = GetObjectCenter();
        lastDirection = transform.forward;
        lastSplineTime = Time.time;

        // Initialize area tracking
        InitializeAreaTracking();

        // Initialize spline system
        if (useSmoothTurnSpline)
        {
            InitializeSplineSystem();
        }
    }

    Vector3 GetObjectCenter()
    {
        // Always use the same method to get object center for consistency
        if (objectRenderer != null)
        {
            return objectRenderer.bounds.center;
        }
        return transform.position;
    }

    void Update()
    {
        // Calculate current speed and velocity for better curve detection using consistent center
        Vector3 currentCenter = GetObjectCenter();
        velocity = (currentCenter - lastPosition) / Time.deltaTime;
        currentSpeed = velocity.magnitude;

        // Update speed-based painting frequency (FS25 style)
        if (useSpeedBasedPainting)
        {
            UpdateSpeedBasedPainting();
        }

        // Update spline system if enabled
        if (useSmoothTurnSpline && currentSpeed >= minSpeed)
        {
            UpdateSplineSystem(currentCenter);
        }

        // Update curve preview for advanced smoothing
        if (useAdvancedCurveSmoothing)
        {
            UpdateCurvePreview(currentCenter);
        }

        lastPosition = currentCenter;

        // Check for terrain contact and paint
        if (currentSpeed >= minSpeed)
        {
            if (useSmoothTurnSpline)
            {
                CheckTerrainContactWithSpline();
            }
            else
            {
                CheckTerrainContactSmooth();
            }
        }

        // Update UI text every frame for real-time display
        UpdateUIText();

        // Handle keyboard shortcuts for speed control
        HandleSpeedControlInput();

        // Reset terrain with R key
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
            if (useSmoothTurnSpline)
            {
                ClearSplineTrail();
            }
        }

        // Complete entire area with Ctrl+C key (for testing/manual trigger)
        if (Input.GetKeyDown(KeyCode.C) && Input.GetKey(KeyCode.LeftControl))
        {
            ManualCompleteEntireArea();
        }
    }

    void CheckTerrainContactSmooth()
    {
        if (terrain == null) return;

        // Get consistent object center position
        Vector3 objectCenter = GetObjectCenter();

        // Raycast downward from object center to check if we're on terrain
        Vector3 rayStart = objectCenter + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2f))
        {
            // Check if we hit the terrain
            if (hit.collider.GetComponent<TerrainCollider>())
            {

                // Use object's center X,Z position but terrain's Y position for accurate painting
                Vector3 exactPaintPos = new Vector3(objectCenter.x + paintOffset.x, hit.point.y, objectCenter.z + paintOffset.z);

                // Use speed-based paint frequency for FS25-style painting
                float activePaintFrequency = useSpeedBasedPainting ? currentPaintFrequency : paintFrequency;

                // Check if enough time has passed since last paint to avoid dotted pattern
                if (Time.time - lastPaintTime >= activePaintFrequency)
                {
                    // Paint directly at current position - NO TRAIL
                    if (IsTargetLayer(exactPaintPos))
                    {
                        PaintTextureAtWithIntensity(exactPaintPos, paintIntensity);
                    }
                    lastPaintTime = Time.time;
                }
            }
        }
    }



    // Alternative collision method (keep both for better compatibility)
    void OnCollisionStay(Collision collision)
    {
        if (terrain == null) return;

        // Check if colliding with Terrain
        if (collision.collider.GetComponent<TerrainCollider>())
        {
            // Loop through all contact points with terrain
            foreach (ContactPoint contact in collision.contacts)
            {
                if (IsTargetLayer(contact.point))
                {
                    PaintTextureAt(contact.point);
                }
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];

        return targetWeight >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        PaintTextureAtWithIntensity(worldPos, 1f);
    }

    void PaintTextureAtWithIntensity(Vector3 worldPos, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        // Convert world position to alphamap coordinates
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;

        // Calculate brush size in alphamap units for square pattern
        float brushSizeX = paintRadius / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = paintRadius / terrainData.size.z * terrainData.alphamapHeight;

        // Use consistent brush size for perfect square
        float brushSize = Mathf.Max(brushSizeX, brushSizeZ);
        int halfBrush = Mathf.RoundToInt(brushSize * 0.5f);

        // Calculate square bounds
        int centerX = Mathf.FloorToInt(mapX);
        int centerZ = Mathf.FloorToInt(mapZ);

        int startX = Mathf.Clamp(centerX - halfBrush, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - halfBrush, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + halfBrush, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + halfBrush, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        // Paint with selected brush pattern
        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    float falloff = 1f;

                    if (useSquareBrush)
                    {
                        // Simple square brush - no rotation
                        falloff = 1f;
                    }
                    else
                    {
                        // Circular brush with distance-based falloff
                        float centerDistX = x - (actualWidth * 0.5f);
                        float centerDistZ = z - (actualHeight * 0.5f);
                        float distance = Mathf.Sqrt(centerDistX * centerDistX + centerDistZ * centerDistZ);
                        float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;

                        if (distance <= maxRadius)
                        {
                            falloff = 1f - (distance / maxRadius);
                        }
                        else
                        {
                            continue; // Skip pixels outside circle
                        }
                    }

                    falloff = Mathf.Clamp01(falloff * intensity);

                    // Apply texture blending
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                            // Count painted pixels for area completion
                            if (alphaMaps[x, z, i] > 0.5f)
                            {
                                paintedPixels++;
                            }
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);

        // Update area progress
        UpdateAreaProgress();
    }

    void OnDrawGizmos()
    {
        if (showDebugGizmos && Application.isPlaying)
        {
            // Show object center using consistent method
            Gizmos.color = Color.blue;
            Vector3 objectCenter = GetObjectCenter();
            Gizmos.DrawWireSphere(objectCenter, 0.5f);

            // Show paint position with offset
            Vector3 paintPos = objectCenter + paintOffset;

            // Show brush gizmo
            Gizmos.color = Color.red;
            if (useSquareBrush)
            {
                Gizmos.DrawWireCube(paintPos, Vector3.one * paintRadius * 2f);
            }
            else
            {
                Gizmos.DrawWireSphere(paintPos, paintRadius);
            }

            // Draw spline system visualization
            if (useSmoothTurnSpline)
            {
                DrawSplineGizmos();
            }
        }
    }

    void DrawSplineGizmos()
    {
        // Draw trail points
        if (trailPoints.Count > 0)
        {
            Gizmos.color = isInTurn ? Color.yellow : Color.cyan;
            foreach (var point in trailPoints)
            {
                Gizmos.DrawWireSphere(point.position, point.width * 0.1f);

                // Draw direction arrows
                Gizmos.color = Color.magenta;
                Gizmos.DrawRay(point.position, point.direction * point.width * 0.3f);
            }

            // Draw connections between trail points
            Gizmos.color = Color.cyan;
            for (int i = 0; i < trailPoints.Count - 1; i++)
            {
                Gizmos.DrawLine(trailPoints[i].position, trailPoints[i + 1].position);
            }
        }

        // Draw smooth spline interpolation
        if (smoothedSpline.Count > 1)
        {
            Gizmos.color = Color.green;
            for (int i = 0; i < smoothedSpline.Count - 1; i++)
            {
                Gizmos.DrawLine(smoothedSpline[i], smoothedSpline[i + 1]);
            }

            // Draw spline points
            Gizmos.color = Color.white;
            foreach (var point in smoothedSpline)
            {
                Gizmos.DrawWireSphere(point, 0.05f);
            }
        }

        // Draw turn indicator
        if (isInTurn && trailPoints.Count > 0)
        {
            Gizmos.color = Color.red;
            Vector3 lastPos = trailPoints[trailPoints.Count - 1].position;
            Gizmos.DrawWireSphere(lastPos, 1f);

            // Show turn angle as text (in Scene view)
            #if UNITY_EDITOR
            UnityEditor.Handles.Label(lastPos + Vector3.up, $"Turn: {currentTurnAngle:F1}°");
            #endif
        }

        // Draw curve preview points (FS25 style)
        if (useAdvancedCurveSmoothing && curvePreviewPoints.Count > 0)
        {
            Gizmos.color = Color.yellow;
            foreach (var previewPoint in curvePreviewPoints)
            {
                Gizmos.DrawWireSphere(previewPoint, 0.15f);
            }

            // Draw preview connections
            Gizmos.color = Color.green;
            for (int i = 0; i < curvePreviewPoints.Count - 1; i++)
            {
                Gizmos.DrawLine(curvePreviewPoints[i], curvePreviewPoints[i + 1]);
            }
        }

        // Draw speed indicator
        if (showSpeedIndicator && useSpeedBasedPainting)
        {
            Vector3 objectCenter = GetObjectCenter();
            Gizmos.color = GetSpeedColor();
            Gizmos.DrawWireCube(objectCenter + Vector3.up * 2f, Vector3.one * 0.5f);

            #if UNITY_EDITOR
            string speedInfo = $"Speed: {currentSpeed:F1} m/s\nMode: {currentSpeedMode}\nFreq: {currentPaintFrequency:F3}";
            UnityEditor.Handles.Label(objectCenter + Vector3.up * 3f, speedInfo);
            #endif
        }
    }

    Color GetSpeedColor()
    {
        if (currentSpeed <= slowPaintingSpeed)
            return Color.green;  // Slow = Green (High Detail)
        else if (currentSpeed >= fastPaintingSpeed)
            return Color.red;    // Fast = Red (Low Detail)
        else
            return Color.yellow; // Normal = Yellow
    }

    void InitializeAreaTracking()
    {
        // Get area size based on selection
        switch (areaSize)
        {
            case AreaSize.Area_20x20:
                currentAreaSize = new Vector2(20, 20);
                break;
            case AreaSize.Area_50x50:
                currentAreaSize = new Vector2(50, 50);
                break;
            case AreaSize.Area_100x100:
                currentAreaSize = new Vector2(100, 100);
                break;
            case AreaSize.Custom:
                currentAreaSize = customAreaSize;
                break;
        }

        // Calculate total pixels in the area
        float pixelsPerMeterX = terrainData.alphamapWidth / terrainData.size.x;
        float pixelsPerMeterZ = terrainData.alphamapHeight / terrainData.size.z;
        totalAreaPixels = Mathf.RoundToInt(currentAreaSize.x * pixelsPerMeterX * currentAreaSize.y * pixelsPerMeterZ);

        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        // Initialize UI Text
        if (percentageText != null)
        {
            percentageText.text = "0.0%";
        }

        // Initialize Progress Slider
        if (progressSlider != null)
        {
            progressSlider.minValue = 0f;
            progressSlider.maxValue = targetCompletionPercentage;
            progressSlider.value = 0f;
        }

        if (completionPanel != null)
        {
            completionPanel.SetActive(false);
        }
    }

    void UpdateAreaProgress()
    {
        if (areaCompleted) return;

        // Calculate completion percentage
        if (totalAreaPixels > 0)
        {
            float completionRatio = (float)paintedPixels / totalAreaPixels;
            currentCompletionPercentage = completionRatio * targetCompletionPercentage;

            // Update UI Text
            if (percentageText != null)
            {
                percentageText.text = currentCompletionPercentage.ToString("F1") + "%";
            }

            // Update Progress Slider
            if (progressSlider != null)
            {
                progressSlider.value = currentCompletionPercentage;
            }

            // Check if area is completed
            if (currentCompletionPercentage >= targetCompletionPercentage)
            {
                areaCompleted = true;
                currentCompletionPercentage = targetCompletionPercentage;

                // Update final percentage text
                if (percentageText != null)
                {
                    percentageText.text = targetCompletionPercentage.ToString("F0") + "%";
                }

                // Update final slider value
                if (progressSlider != null)
                {
                    progressSlider.value = targetCompletionPercentage;
                }

                // Handle camera switching and completion panel sequence
                StartCoroutine(HandleAreaCompletionSequence());

                // Auto-complete the entire area if enabled
                if (autoCompleteAreaOnFinish)
                {
                    CompleteEntireTargetArea();
                }

                Debug.Log("Area Completed! Percentage: " + currentCompletionPercentage + "%");
            }
        }
    }

    /// <summary>
    /// Handles the camera switching sequence when area is completed
    /// </summary>
    System.Collections.IEnumerator HandleAreaCompletionSequence()
    {
        // Step 1: Set machinecam to false
        if (machinecam != null)
        {
            machinecam.SetActive(false);
            Debug.Log("Machine camera disabled");
        }

        // Step 2: Set canvas to false
        if (canvas != null)
        {
            canvas.SetActive(false);
            Debug.Log("Canvas disabled");
        }

        // Step 3: Set tractorcam to true
        if (tractorcam != null)
        {
            tractorcam.SetActive(true);
            Debug.Log("Tractor camera enabled");
        }

        // Step 4: Wait for 10 seconds
        Debug.Log("Waiting 10 seconds before showing completion panel...");
        yield return new WaitForSeconds(10f);

        // Step 5: Show completion panel
        if (completionPanel != null)
        {
            completionPanel.SetActive(true);
            Debug.Log("Completion panel shown after 10 second delay");
        }
    }

    [ContextMenu("Reset Area Progress")]
    public void ResetAreaProgress()
    {
        paintedPixels = 0;
        currentCompletionPercentage = 0f;
        areaCompleted = false;

        // Reset UI Text
        if (percentageText != null)
        {
            percentageText.text = "0.0%";
        }

        // Reset Progress Slider
        if (progressSlider != null)
        {
            progressSlider.value = 0f;
        }

        if (completionPanel != null)
        {
            completionPanel.SetActive(false);
        }

        Debug.Log("Area progress reset!");
    }

    /// <summary>
    /// Completely paints the entire target layer area when completion panel is activated
    /// </summary>
    void CompleteEntireTargetArea()
    {
        if (terrain == null || terrainData == null)
        {
            Debug.LogWarning("Cannot complete area: Terrain or TerrainData is null!");
            return;
        }

        if (targetLayerIndex < 0 || targetLayerIndex >= terrainData.alphamapLayers)
        {
            Debug.LogWarning($"Invalid target layer index: {targetLayerIndex}. Must be between 0 and {terrainData.alphamapLayers - 1}");
            return;
        }

        if (applyLayerIndex < 0 || applyLayerIndex >= terrainData.alphamapLayers)
        {
            Debug.LogWarning($"Invalid apply layer index: {applyLayerIndex}. Must be between 0 and {terrainData.alphamapLayers - 1}");
            return;
        }

        Debug.Log($"Auto-completing entire target area... Target Layer: {targetLayerIndex}, Apply Layer: {applyLayerIndex}");

        // Get the entire terrain alphamap
        float[,,] alphaMaps = terrainData.GetAlphamaps(0, 0, terrainData.alphamapWidth, terrainData.alphamapHeight);

        int completedPixels = 0;
        int totalProcessedPixels = 0;
        int targetPixelsFound = 0;

        // Process entire terrain
        for (int x = 0; x < terrainData.alphamapWidth; x++)
        {
            for (int z = 0; z < terrainData.alphamapHeight; z++)
            {
                // Check if this pixel has target layer (e.g., grass)
                float targetWeight = alphaMaps[x, z, targetLayerIndex];

                if (targetWeight >= 0.5f) // If target layer is present
                {
                    targetPixelsFound++;
                    totalProcessedPixels++;

                    // Apply the apply layer (e.g., soil) completely
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = 1f; // Set to maximum
                            completedPixels++;
                        }
                        else
                        {
                            alphaMaps[x, z, i] = 0f; // Remove other layers
                        }
                    }
                }
            }
        }

        // Apply the changes to terrain
        terrainData.SetAlphamaps(0, 0, alphaMaps);

        Debug.Log($"Area auto-completion finished!");
        Debug.Log($"- Target pixels found: {targetPixelsFound}");
        Debug.Log($"- Total processed pixels: {totalProcessedPixels}");
        Debug.Log($"- Completed pixels: {completedPixels}");
        Debug.Log($"- Terrain size: {terrainData.alphamapWidth}x{terrainData.alphamapHeight}");
    }

    [ContextMenu("Complete Entire Target Area")]
    public void ManualCompleteEntireArea()
    {
        CompleteEntireTargetArea();
        Debug.Log("Manual area completion triggered!");
    }

    [ContextMenu("Toggle Auto Complete Area")]
    public void ToggleAutoCompleteArea()
    {
        autoCompleteAreaOnFinish = !autoCompleteAreaOnFinish;
        Debug.Log($"Auto Complete Area on Finish: {(autoCompleteAreaOnFinish ? "ENABLED" : "DISABLED")}");

        if (autoCompleteAreaOnFinish)
        {
            Debug.Log("When completion panel shows, entire target area will be automatically painted!");
        }
        else
        {
            Debug.Log("Auto-completion disabled. Only manual painting will work.");
        }
    }

    void UpdateUIText()
    {
        if (!areaCompleted)
        {
            // Calculate current percentage for display
            if (totalAreaPixels > 0)
            {
                float completionRatio = (float)paintedPixels / totalAreaPixels;
                float displayPercentage = completionRatio * targetCompletionPercentage;

                // Update percentage text
                if (percentageText != null)
                {
                    percentageText.text = displayPercentage.ToString("F1") + "%";
                }

                // Update progress slider
                if (progressSlider != null)
                {
                    progressSlider.value = displayPercentage;
                }
            }
            else
            {
                // Reset to zero when no progress
                if (percentageText != null)
                {
                    percentageText.text = "0.0%";
                }

                if (progressSlider != null)
                {
                    progressSlider.value = 0f;
                }
            }
        }
    }

    // ===== FARMING SIMULATOR 2025 SPEED CONTROL METHODS =====

    void UpdateSpeedBasedPainting()
    {
        // Calculate speed-based paint frequency like FS25
        float speedRatio = Mathf.Clamp01((currentSpeed - minSpeed) / (fastPaintingSpeed - minSpeed));
        float curveValue = speedPaintingCurve.Evaluate(speedRatio);

        // Invert the curve - slower speed = more frequent painting (higher detail)
        currentPaintFrequency = Mathf.Lerp(minPaintFrequency, maxPaintFrequency, 1f - curveValue);

        // Update speed mode indicator
        if (currentSpeed <= slowPaintingSpeed)
        {
            currentSpeedMode = "Slow (High Detail)";
        }
        else if (currentSpeed >= fastPaintingSpeed)
        {
            currentSpeedMode = "Fast (Low Detail)";
        }
        else
        {
            currentSpeedMode = "Normal";
        }

        // Adaptive spline resolution based on speed
        if (adaptiveSplineResolution)
        {
            adaptiveResolution = Mathf.Lerp(0.1f, 0.5f, speedRatio);
        }
        else
        {
            adaptiveResolution = splineResolution;
        }
    }

    void UpdateCurvePreview(Vector3 currentCenter)
    {
        // Generate curve preview points for ultra-smooth painting
        curvePreviewPoints.Clear();

        if (trailPoints.Count >= 2)
        {
            Vector3 predictedDirection = velocity.normalized;
            Vector3 currentDirection = lastDirection;

            // Predict future curve direction
            Vector3 blendedDirection = Vector3.Slerp(currentDirection, predictedDirection, curvePredictionStrength);

            // Generate preview points ahead of current position
            for (int i = 1; i <= 5; i++)
            {
                float distance = i * (curvePreviewDistance / 5f);
                Vector3 previewPoint = currentCenter + blendedDirection * distance;
                curvePreviewPoints.Add(previewPoint);
            }
        }
    }

    void HandleSpeedControlInput()
    {
        // Keyboard shortcuts for speed control (FS25 style)
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            SetPaintingMode("Slow");
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            SetPaintingMode("Normal");
        }
        else if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            SetPaintingMode("Fast");
        }

        // Mouse wheel for real-time speed adjustment
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f && Input.GetKey(KeyCode.LeftControl))
        {
            paintFrequency = Mathf.Clamp(paintFrequency + scroll * 0.02f, minPaintFrequency, maxPaintFrequency);
        }
    }

    public void SetPaintingMode(string mode)
    {
        switch (mode.ToLower())
        {
            case "slow":
                paintFrequency = minPaintFrequency;
                currentSpeedMode = "Slow (High Detail)";
                Debug.Log("Painting Mode: SLOW - High Detail (like FS25 precision mode)");
                break;
            case "normal":
                paintFrequency = (minPaintFrequency + maxPaintFrequency) * 0.5f;
                currentSpeedMode = "Normal";
                Debug.Log("Painting Mode: NORMAL - Balanced");
                break;
            case "fast":
                paintFrequency = maxPaintFrequency;
                currentSpeedMode = "Fast (Low Detail)";
                Debug.Log("Painting Mode: FAST - Quick Coverage");
                break;
        }
    }

    // ===== SMOOTH TURN SPLINE SYSTEM METHODS =====

    void InitializeSplineSystem()
    {
        trailPoints.Clear();
        smoothedSpline.Clear();
        curvePreviewPoints.Clear();
        lastSplineTime = Time.time;
        isInTurn = false;
        currentTurnAngle = 0f;
        currentPaintFrequency = paintFrequency;
        Debug.Log("Smooth Turn Spline System initialized with FS25 speed control!");
    }

    void UpdateSplineSystem(Vector3 currentCenter)
    {
        Vector3 currentDirection = velocity.normalized;

        // Calculate turn angle
        if (lastDirection != Vector3.zero && currentDirection != Vector3.zero)
        {
            currentTurnAngle = Vector3.Angle(lastDirection, currentDirection);
            isInTurn = currentTurnAngle > turnDetectionAngle;
        }

        // Add trail points based on movement and adaptive resolution
        float activeResolution = adaptiveSplineResolution ? adaptiveResolution : splineResolution;
        if (Time.time - lastSplineTime >= activeResolution)
        {
            AddSplineTrailPoint(currentCenter, currentDirection);
            lastSplineTime = Time.time;
        }

        // Clean old trail points
        if (autoCleanOldTrail)
        {
            CleanOldSplineTrailPoints();
        }

        // Generate smooth spline if we have enough points
        if (trailPoints.Count > 2)
        {
            GenerateSmoothSpline();
        }

        lastDirection = currentDirection;
    }

    void AddSplineTrailPoint(Vector3 position, Vector3 direction)
    {
        // Calculate adaptive width based on turn angle
        float currentWidth = paintRadius;

        if (adaptiveWidthOnTurns && isInTurn)
        {
            float turnFactor = Mathf.Clamp01(currentTurnAngle / maxTurnAngle);
            currentWidth = Mathf.Lerp(minTurnWidth, maxTurnWidth, turnWidthCurve.Evaluate(turnFactor));
        }

        // Calculate adaptive intensity for smoother transitions
        float currentIntensity = paintIntensity;
        if (isInTurn)
        {
            float turnFactor = Mathf.Clamp01(currentTurnAngle / maxTurnAngle);
            currentIntensity *= turnIntensityCurve.Evaluate(turnFactor);
        }

        SmoothTrailPoint newPoint = new SmoothTrailPoint(position, direction, currentWidth, currentIntensity, currentTurnAngle);
        trailPoints.Add(newPoint);

        // Limit trail length
        if (trailPoints.Count > maxTrailLength)
        {
            trailPoints.RemoveAt(0);
        }
    }

    void CleanOldSplineTrailPoints()
    {
        float currentTime = Time.time;
        trailPoints.RemoveAll(point => currentTime - point.timestamp > trailLifetime);
    }

    void GenerateSmoothSpline()
    {
        smoothedSpline.Clear();

        if (trailPoints.Count < 3) return;

        // Apply smoothing iterations
        List<Vector3> positions = new List<Vector3>();
        foreach (var point in trailPoints)
        {
            positions.Add(point.position);
        }

        // Use adaptive resolution for better performance
        float currentResolution = adaptiveSplineResolution ? adaptiveResolution : splineResolution;

        // Apply standard smoothing iterations
        for (int iteration = 0; iteration < smoothingIterations; iteration++)
        {
            positions = ApplySplineSmoothing(positions);
        }

        // Apply advanced curve smoothing if enabled (FS25 style)
        if (useAdvancedCurveSmoothing)
        {
            positions = ApplyAdvancedCurveSmoothing(positions);
        }

        // Include curve preview points for ultra-smooth transitions
        if (useAdvancedCurveSmoothing && curvePreviewPoints.Count > 0)
        {
            positions.AddRange(curvePreviewPoints);
        }

        // Generate interpolated spline points using Catmull-Rom spline
        for (int i = 0; i < positions.Count - 1; i++)
        {
            Vector3 p0 = i > 0 ? positions[i - 1] : positions[i];
            Vector3 p1 = positions[i];
            Vector3 p2 = positions[i + 1];
            Vector3 p3 = i < positions.Count - 2 ? positions[i + 2] : positions[i + 1];

            // Calculate segments based on distance and adaptive resolution
            float distance = Vector3.Distance(p1, p2);
            int segments = Mathf.CeilToInt(distance / (currentResolution * 0.5f));
            segments = Mathf.Max(1, segments); // Ensure at least 1 segment

            // Use more segments for sharp turns (FS25 style)
            if (isInTurn && currentTurnAngle > 20f)
            {
                segments = Mathf.RoundToInt(segments * 1.5f);
            }

            for (int j = 0; j <= segments; j++)
            {
                float t = (float)j / segments;
                Vector3 interpolatedPoint = CatmullRomInterpolation(p0, p1, p2, p3, t);
                smoothedSpline.Add(interpolatedPoint);
            }
        }
    }

    List<Vector3> ApplyAdvancedCurveSmoothing(List<Vector3> points)
    {
        List<Vector3> smoothed = new List<Vector3>(points);

        // Apply extra smoothing passes for ultra-smooth curves
        for (int pass = 0; pass < advancedSmoothingPasses; pass++)
        {
            for (int i = 1; i < smoothed.Count - 1; i++)
            {
                Vector3 prev = smoothed[i - 1];
                Vector3 current = smoothed[i];
                Vector3 next = smoothed[i + 1];

                // Use corner smoothing radius for sharp turns
                float smoothingStrength = smoothingFactor;
                if (isInTurn)
                {
                    float distance = Vector3.Distance(prev, next);
                    if (distance < cornerSmoothingRadius)
                    {
                        smoothingStrength *= 1.5f; // Extra smoothing for tight corners
                    }
                }

                Vector3 smoothedPoint = Vector3.Lerp(current, (prev + next) * 0.5f, smoothingStrength);
                smoothed[i] = smoothedPoint;
            }
        }

        return smoothed;
    }

    List<Vector3> ApplySplineSmoothing(List<Vector3> points)
    {
        List<Vector3> smoothed = new List<Vector3>(points);

        for (int i = 1; i < points.Count - 1; i++)
        {
            Vector3 prev = points[i - 1];
            Vector3 current = points[i];
            Vector3 next = points[i + 1];

            Vector3 smoothedPoint = Vector3.Lerp(current, (prev + next) * 0.5f, smoothingFactor);
            smoothed[i] = smoothedPoint;
        }

        return smoothed;
    }

    Vector3 CatmullRomInterpolation(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;

        Vector3 result = 0.5f * (
            (2f * p1) +
            (-p0 + p2) * t +
            (2f * p0 - 5f * p1 + 4f * p2 - p3) * t2 +
            (-p0 + 3f * p1 - 3f * p2 + p3) * t3
        );

        return result;
    }

    void CheckTerrainContactWithSpline()
    {
        if (terrain == null || smoothedSpline.Count == 0) return;

        // Paint along the smoothed spline for seamless texture transitions
        for (int i = 0; i < smoothedSpline.Count; i++)
        {
            Vector3 splinePoint = smoothedSpline[i];

            // Raycast to ensure we're on terrain
            Vector3 rayStart = splinePoint + Vector3.up * 0.5f;
            if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2f))
            {
                if (hit.collider.GetComponent<TerrainCollider>())
                {
                    Vector3 paintPos = new Vector3(splinePoint.x + paintOffset.x, hit.point.y, splinePoint.z + paintOffset.z);

                    // Check if target layer is present before painting
                    if (IsTargetLayer(paintPos))
                    {
                        // Calculate adaptive parameters based on spline position
                        float t = (float)i / (smoothedSpline.Count - 1);
                        float adaptiveWidth = paintRadius;
                        float adaptiveIntensity = paintIntensity;

                        // Find corresponding trail point for adaptive parameters
                        if (trailPoints.Count > 0)
                        {
                            int trailIndex = Mathf.RoundToInt(t * (trailPoints.Count - 1));
                            trailIndex = Mathf.Clamp(trailIndex, 0, trailPoints.Count - 1);

                            var trailPoint = trailPoints[trailIndex];
                            adaptiveWidth = trailPoint.width;
                            adaptiveIntensity = trailPoint.intensity;
                        }

                        PaintTextureAtWithSeamlessTransition(paintPos, adaptiveWidth, adaptiveIntensity);
                    }
                }
            }
        }
    }

    void PaintTextureAtWithSeamlessTransition(Vector3 worldPos, float width, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        // Convert world position to alphamap coordinates
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;

        // Calculate brush size in alphamap units
        float brushSizeX = width / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = width / terrainData.size.z * terrainData.alphamapHeight;
        float brushSize = Mathf.Max(brushSizeX, brushSizeZ);
        int halfBrush = Mathf.RoundToInt(brushSize * 0.5f);

        // Calculate bounds with edge feathering
        int centerX = Mathf.FloorToInt(mapX);
        int centerZ = Mathf.FloorToInt(mapZ);
        int featherBrush = Mathf.RoundToInt(halfBrush * edgeFeathering);

        int startX = Mathf.Clamp(centerX - featherBrush, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - featherBrush, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + featherBrush, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + featherBrush, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;

        if (actualWidth <= 0 || actualHeight <= 0) return;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        // Paint with seamless transition
        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    // Calculate distance-based falloff for seamless blending
                    float offsetX = x - (actualWidth * 0.5f);
                    float offsetZ = z - (actualHeight * 0.5f);
                    float distance = Mathf.Sqrt(offsetX * offsetX + offsetZ * offsetZ);
                    float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;

                    float falloff = 1f;
                    if (useSeamlessTransition)
                    {
                        // Smooth falloff with transition smoothness
                        falloff = Mathf.SmoothStep(1f, 0f, distance / maxRadius);
                        falloff = Mathf.Pow(falloff, transitionSmoothness);
                    }
                    else if (!useSquareBrush)
                    {
                        // Standard circular falloff
                        if (distance <= maxRadius)
                        {
                            falloff = 1f - (distance / maxRadius);
                        }
                        else
                        {
                            continue;
                        }
                    }

                    falloff = Mathf.Clamp01(falloff * intensity);

                    // Apply texture blending
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                            // Count painted pixels for area completion
                            if (alphaMaps[x, z, i] > 0.5f)
                            {
                                paintedPixels++;
                            }
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);

        // Update area progress
        UpdateAreaProgress();
    }

    [ContextMenu("Clear Spline Trail")]
    public void ClearSplineTrail()
    {
        trailPoints.Clear();
        smoothedSpline.Clear();
        isInTurn = false;
        currentTurnAngle = 0f;
        Debug.Log("Spline trail cleared!");
    }

    [ContextMenu("Toggle Smooth Turn Spline")]
    public void ToggleSmoothTurnSpline()
    {
        useSmoothTurnSpline = !useSmoothTurnSpline;

        if (useSmoothTurnSpline)
        {
            InitializeSplineSystem();
            Debug.Log("Smooth Turn Spline System ENABLED - Like Farming Simulator 2025!");
        }
        else
        {
            ClearSplineTrail();
            Debug.Log("Smooth Turn Spline System DISABLED - Using standard painting");
        }
    }

    [ContextMenu("Test Spline System")]
    public void TestSplineSystem()
    {
        if (!useSmoothTurnSpline)
        {
            Debug.LogWarning("Spline system is disabled. Enable it first!");
            return;
        }

        Debug.Log($"=== FARMING SIMULATOR 2025 STYLE SYSTEM STATUS ===");
        Debug.Log($"Trail Points: {trailPoints.Count}");
        Debug.Log($"Smoothed Spline Points: {smoothedSpline.Count}");
        Debug.Log($"Is In Turn: {isInTurn}");
        Debug.Log($"Current Turn Angle: {currentTurnAngle:F1}°");
        Debug.Log($"Current Speed: {currentSpeed:F2} m/s");
        Debug.Log($"Speed Mode: {currentSpeedMode}");
        Debug.Log($"Paint Frequency: {currentPaintFrequency:F3}");
        Debug.Log($"Adaptive Resolution: {adaptiveResolution:F3}");
        Debug.Log($"Advanced Curve Smoothing: {(useAdvancedCurveSmoothing ? "Enabled" : "Disabled")}");
        Debug.Log($"Speed-Based Painting: {(useSpeedBasedPainting ? "Enabled" : "Disabled")}");
        Debug.Log($"Curve Preview Points: {curvePreviewPoints.Count}");

        if (trailPoints.Count > 0)
        {
            var lastPoint = trailPoints[trailPoints.Count - 1];
            Debug.Log($"Last Trail Point - Width: {lastPoint.width:F2}, Intensity: {lastPoint.intensity:F2}");
        }
    }

    [ContextMenu("Set Slow Painting Mode")]
    public void SetSlowPaintingMode()
    {
        SetPaintingMode("Slow");
    }

    [ContextMenu("Set Normal Painting Mode")]
    public void SetNormalPaintingMode()
    {
        SetPaintingMode("Normal");
    }

    [ContextMenu("Set Fast Painting Mode")]
    public void SetFastPaintingMode()
    {
        SetPaintingMode("Fast");
    }

    [ContextMenu("Toggle Speed-Based Painting")]
    public void ToggleSpeedBasedPainting()
    {
        useSpeedBasedPainting = !useSpeedBasedPainting;
        Debug.Log($"Speed-Based Painting: {(useSpeedBasedPainting ? "ENABLED" : "DISABLED")}");

        if (useSpeedBasedPainting)
        {
            Debug.Log("Now painting speed adapts like Farming Simulator 2025!");
            Debug.Log("Slow speed = High detail, Fast speed = Quick coverage");
        }
    }

    [ContextMenu("Toggle Advanced Curve Smoothing")]
    public void ToggleAdvancedCurveSmoothing()
    {
        useAdvancedCurveSmoothing = !useAdvancedCurveSmoothing;
        Debug.Log($"Advanced Curve Smoothing: {(useAdvancedCurveSmoothing ? "ENABLED" : "DISABLED")}");

        if (useAdvancedCurveSmoothing)
        {
            Debug.Log("Ultra-smooth curves like FS25 activated!");
        }
    }

}
