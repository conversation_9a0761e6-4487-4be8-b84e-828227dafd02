using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class TrailPoint
{
    public Vector3 position;
    public Vector3 direction;
    public float width;
    public float intensity;
    public float timestamp;
    
    public TrailPoint(Vector3 pos, Vector3 dir, float w = 2f, float i = 1f)
    {
        position = pos;
        direction = dir.normalized;
        width = w;
        intensity = i;
        timestamp = Time.time;
    }
}

public class TrailSplinePainter : MonoBehaviour
{
    [Header("Trail Settings")]
    public float trailWidth = 3f;
    public float trailResolution = 0.5f;
    public float minSpeed = 0.1f;
    public float maxTrailLength = 100f;
    public bool autoCleanOldTrail = true;
    public float trailLifetime = 30f;
    
    [Header("Spline Smoothing")]
    public bool useSplineSmoothing = true;
    public float smoothingFactor = 0.3f;
    public int smoothingIterations = 3;
    public AnimationCurve widthCurve = AnimationCurve.Linear(0, 1, 1, 1);
    public AnimationCurve intensityCurve = AnimationCurve.Linear(0, 1, 1, 1);
    
    [Header("Turn Smoothing")]
    public float turnSmoothingRadius = 2f;
    public float maxTurnAngle = 45f;
    public bool adaptiveWidth = true;
    public float minTurnWidth = 1.5f;
    public float maxTurnWidth = 4f;
    
    [Header("Terrain Painting")]
    public int targetLayerIndex = 0;
    public int applyLayerIndex = 1;
    public float paintIntensity = 1f;
    public bool paintRealtime = true;
    
    [Header("Debug")]
    public bool showDebugGizmos = true;
    public bool showTrailPoints = true;
    public bool showSplineInterpolation = true;
    
    private List<TrailPoint> trailPoints = new List<TrailPoint>();
    private List<Vector3> smoothedSpline = new List<Vector3>();
    private Terrain terrain;
    private TerrainData terrainData;
    private Vector3 lastPosition;
    private Vector3 velocity;
    private float lastTrailTime;
    private Renderer objectRenderer;
    
    void Start()
    {
        terrain = Terrain.activeTerrain;
        objectRenderer = GetComponent<Renderer>();
        
        if (terrain != null)
        {
            terrainData = terrain.terrainData;
        }
        
        lastPosition = GetObjectCenter();
        lastTrailTime = Time.time;
    }
    
    Vector3 GetObjectCenter()
    {
        return objectRenderer != null ? objectRenderer.bounds.center : transform.position;
    }
    
    void Update()
    {
        Vector3 currentCenter = GetObjectCenter();
        velocity = (currentCenter - lastPosition) / Time.deltaTime;
        
        // Add trail points based on movement
        if (velocity.magnitude >= minSpeed && Time.time - lastTrailTime >= trailResolution)
        {
            AddTrailPoint(currentCenter, velocity.normalized);
            lastTrailTime = Time.time;
        }
        
        // Clean old trail points
        if (autoCleanOldTrail)
        {
            CleanOldTrailPoints();
        }
        
        // Generate smooth spline
        if (useSplineSmoothing && trailPoints.Count > 2)
        {
            GenerateSmoothSpline();
        }
        
        // Paint terrain in real-time
        if (paintRealtime && trailPoints.Count > 1)
        {
            PaintTrailOnTerrain();
        }
        
        lastPosition = currentCenter;
    }
    
    void AddTrailPoint(Vector3 position, Vector3 direction)
    {
        // Calculate adaptive width based on turn angle
        float currentWidth = trailWidth;
        
        if (adaptiveWidth && trailPoints.Count > 0)
        {
            Vector3 lastDirection = trailPoints[trailPoints.Count - 1].direction;
            float turnAngle = Vector3.Angle(lastDirection, direction);
            
            if (turnAngle > 5f) // Only adjust for significant turns
            {
                float turnFactor = Mathf.Clamp01(turnAngle / maxTurnAngle);
                currentWidth = Mathf.Lerp(minTurnWidth, maxTurnWidth, turnFactor);
            }
        }
        
        TrailPoint newPoint = new TrailPoint(position, direction, currentWidth, paintIntensity);
        trailPoints.Add(newPoint);
        
        // Limit trail length
        if (trailPoints.Count > maxTrailLength)
        {
            trailPoints.RemoveAt(0);
        }
    }
    
    void CleanOldTrailPoints()
    {
        float currentTime = Time.time;
        trailPoints.RemoveAll(point => currentTime - point.timestamp > trailLifetime);
    }
    
    void GenerateSmoothSpline()
    {
        smoothedSpline.Clear();
        
        if (trailPoints.Count < 3) return;
        
        // Apply smoothing iterations
        List<Vector3> positions = new List<Vector3>();
        foreach (var point in trailPoints)
        {
            positions.Add(point.position);
        }
        
        for (int iteration = 0; iteration < smoothingIterations; iteration++)
        {
            positions = ApplySplineSmoothing(positions);
        }
        
        // Generate interpolated spline points
        for (int i = 0; i < positions.Count - 1; i++)
        {
            Vector3 p0 = i > 0 ? positions[i - 1] : positions[i];
            Vector3 p1 = positions[i];
            Vector3 p2 = positions[i + 1];
            Vector3 p3 = i < positions.Count - 2 ? positions[i + 2] : positions[i + 1];
            
            // Catmull-Rom spline interpolation for smooth curves
            int segments = Mathf.CeilToInt(Vector3.Distance(p1, p2) / (trailResolution * 0.5f));
            
            for (int j = 0; j <= segments; j++)
            {
                float t = (float)j / segments;
                Vector3 interpolatedPoint = CatmullRomInterpolation(p0, p1, p2, p3, t);
                smoothedSpline.Add(interpolatedPoint);
            }
        }
    }
    
    List<Vector3> ApplySplineSmoothing(List<Vector3> points)
    {
        List<Vector3> smoothed = new List<Vector3>(points);
        
        for (int i = 1; i < points.Count - 1; i++)
        {
            Vector3 prev = points[i - 1];
            Vector3 current = points[i];
            Vector3 next = points[i + 1];
            
            Vector3 smoothedPoint = Vector3.Lerp(current, (prev + next) * 0.5f, smoothingFactor);
            smoothed[i] = smoothedPoint;
        }
        
        return smoothed;
    }
    
    Vector3 CatmullRomInterpolation(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;
        
        Vector3 result = 0.5f * (
            (2f * p1) +
            (-p0 + p2) * t +
            (2f * p0 - 5f * p1 + 4f * p2 - p3) * t2 +
            (-p0 + 3f * p1 - 3f * p2 + p3) * t3
        );
        
        return result;
    }
    
    void PaintTrailOnTerrain()
    {
        if (terrain == null) return;
        
        List<Vector3> paintPoints = useSplineSmoothing ? smoothedSpline : GetTrailPositions();
        
        for (int i = 0; i < paintPoints.Count; i++)
        {
            float t = (float)i / (paintPoints.Count - 1);
            float currentWidth = trailWidth * widthCurve.Evaluate(t);
            float currentIntensity = paintIntensity * intensityCurve.Evaluate(t);
            
            PaintTextureAtPosition(paintPoints[i], currentWidth, currentIntensity);
        }
    }
    
    List<Vector3> GetTrailPositions()
    {
        List<Vector3> positions = new List<Vector3>();
        foreach (var point in trailPoints)
        {
            positions.Add(point.position);
        }
        return positions;
    }
    
    void PaintTextureAtPosition(Vector3 worldPos, float width, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;
        
        float brushSizeX = width / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = width / terrainData.size.z * terrainData.alphamapHeight;
        float brushSize = Mathf.Max(brushSizeX, brushSizeZ);
        int halfBrush = Mathf.RoundToInt(brushSize * 0.5f);
        
        int centerX = Mathf.FloorToInt(mapX);
        int centerZ = Mathf.FloorToInt(mapZ);
        int startX = Mathf.Clamp(centerX - halfBrush, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - halfBrush, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + halfBrush, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + halfBrush, 0, terrainData.alphamapHeight - 1);
        
        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;
        
        if (actualWidth <= 0 || actualHeight <= 0) return;
        
        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);
        
        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float offsetX = x - (actualWidth * 0.5f);
                float offsetZ = z - (actualHeight * 0.5f);
                float distance = Mathf.Sqrt(offsetX * offsetX + offsetZ * offsetZ);
                float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;
                
                if (distance <= maxRadius)
                {
                    float falloff = Mathf.Clamp01((1f - (distance / maxRadius)) * intensity);
                    
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }
        
        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }
    
    [ContextMenu("Clear Trail")]
    public void ClearTrail()
    {
        trailPoints.Clear();
        smoothedSpline.Clear();
        Debug.Log("Trail cleared!");
    }
    
    [ContextMenu("Force Paint Trail")]
    public void ForcePaintTrail()
    {
        if (trailPoints.Count > 1)
        {
            PaintTrailOnTerrain();
            Debug.Log("Trail painted on terrain!");
        }
    }

    void OnDrawGizmos()
    {
        if (!showDebugGizmos) return;

        // Draw current object position
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(GetObjectCenter(), 0.5f);

        // Draw trail points
        if (showTrailPoints && trailPoints.Count > 0)
        {
            Gizmos.color = Color.red;
            foreach (var point in trailPoints)
            {
                Gizmos.DrawWireSphere(point.position, point.width * 0.1f);

                // Draw direction arrows
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(point.position, point.direction * point.width * 0.5f);
            }

            // Draw connections between trail points
            Gizmos.color = Color.red;
            for (int i = 0; i < trailPoints.Count - 1; i++)
            {
                Gizmos.DrawLine(trailPoints[i].position, trailPoints[i + 1].position);
            }
        }

        // Draw smooth spline interpolation
        if (showSplineInterpolation && smoothedSpline.Count > 1)
        {
            Gizmos.color = Color.green;
            for (int i = 0; i < smoothedSpline.Count - 1; i++)
            {
                Gizmos.DrawLine(smoothedSpline[i], smoothedSpline[i + 1]);
            }

            // Draw spline points
            Gizmos.color = Color.cyan;
            foreach (var point in smoothedSpline)
            {
                Gizmos.DrawWireSphere(point, 0.1f);
            }
        }

        // Draw turn smoothing radius
        if (trailPoints.Count > 0)
        {
            Gizmos.color = Color.magenta;
            Vector3 lastPos = trailPoints[trailPoints.Count - 1].position;
            Gizmos.DrawWireSphere(lastPos, turnSmoothingRadius);
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw detailed information when selected
        if (trailPoints.Count > 0)
        {
            Gizmos.color = Color.white;
            for (int i = 0; i < trailPoints.Count; i++)
            {
                var point = trailPoints[i];

                // Draw width visualization
                Vector3 perpendicular = Vector3.Cross(point.direction, Vector3.up).normalized;
                Vector3 left = point.position + perpendicular * point.width * 0.5f;
                Vector3 right = point.position - perpendicular * point.width * 0.5f;

                Gizmos.DrawLine(left, right);
                Gizmos.DrawWireSphere(left, 0.1f);
                Gizmos.DrawWireSphere(right, 0.1f);
            }
        }
    }
}
