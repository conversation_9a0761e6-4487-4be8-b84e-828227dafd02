using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script to showcase the Smooth Turn Spline System for TerrainPainter
/// This script provides UI controls and automatic testing for the spline system
/// </summary>
public class SmoothTurnSplineDemo : MonoBehaviour
{
    [Header("TerrainPainter Reference")]
    public TerrainPainter terrainPainter;
    
    [Header("UI Controls")]
    public Button toggleSplineButton;
    public Button clearTrailButton;
    public Button testSystemButton;
    public Text statusText;
    public Slider smoothingFactorSlider;
    public Slider splineResolutionSlider;
    public Slider transitionSmoothnessSlider;
    
    [Header("Demo Settings")]
    public bool autoDemo = false;
    public float demoSpeed = 5f;
    public float demoRadius = 10f;
    public bool showInstructions = true;
    
    private bool isDemoRunning = false;
    private float demoTime = 0f;
    
    void Start()
    {
        // Get TerrainPainter if not assigned
        if (terrainPainter == null)
            terrainPainter = FindObjectOfType<TerrainPainter>();
        
        // Setup UI buttons
        SetupUI();
        
        // Show initial status
        UpdateStatusText();
        
        if (showInstructions)
        {
            ShowInstructions();
        }
    }
    
    void SetupUI()
    {
        if (toggleSplineButton != null)
        {
            toggleSplineButton.onClick.AddListener(ToggleSplineSystem);
            UpdateButtonText();
        }
        
        if (clearTrailButton != null)
            clearTrailButton.onClick.AddListener(ClearTrail);
        
        if (testSystemButton != null)
            testSystemButton.onClick.AddListener(TestSystem);
        
        // Setup sliders
        if (smoothingFactorSlider != null && terrainPainter != null)
        {
            smoothingFactorSlider.value = terrainPainter.smoothingFactor;
            smoothingFactorSlider.onValueChanged.AddListener(OnSmoothingFactorChanged);
        }
        
        if (splineResolutionSlider != null && terrainPainter != null)
        {
            splineResolutionSlider.value = terrainPainter.splineResolution;
            splineResolutionSlider.onValueChanged.AddListener(OnSplineResolutionChanged);
        }
        
        if (transitionSmoothnessSlider != null && terrainPainter != null)
        {
            transitionSmoothnessSlider.value = terrainPainter.transitionSmoothness;
            transitionSmoothnessSlider.onValueChanged.AddListener(OnTransitionSmoothnessChanged);
        }
    }
    
    void Update()
    {
        // Update status text
        UpdateStatusText();
        
        // Handle demo mode
        if (autoDemo && isDemoRunning)
        {
            RunAutoDemo();
        }
        
        // Handle keyboard shortcuts
        HandleKeyboardInput();
    }
    
    void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(KeyCode.F1))
            ToggleSplineSystem();

        if (Input.GetKeyDown(KeyCode.F2))
            ClearTrail();

        if (Input.GetKeyDown(KeyCode.F3))
            TestSystem();

        if (Input.GetKeyDown(KeyCode.F4))
            ToggleAutoDemo();

        if (Input.GetKeyDown(KeyCode.F5))
            ShowInstructions();

        // Speed control shortcuts (FS25 style)
        if (Input.GetKeyDown(KeyCode.Alpha1))
            SetPaintingSpeed("Slow");

        if (Input.GetKeyDown(KeyCode.Alpha2))
            SetPaintingSpeed("Normal");

        if (Input.GetKeyDown(KeyCode.Alpha3))
            SetPaintingSpeed("Fast");

        if (Input.GetKeyDown(KeyCode.F6))
            ToggleSpeedBasedPainting();

        if (Input.GetKeyDown(KeyCode.F7))
            ToggleAdvancedCurveSmoothing();
    }
    
    void RunAutoDemo()
    {
        if (terrainPainter == null) return;
        
        demoTime += Time.deltaTime * demoSpeed;
        
        // Create a figure-8 pattern for testing turns
        float x = Mathf.Sin(demoTime) * demoRadius;
        float z = Mathf.Sin(demoTime * 2f) * demoRadius * 0.5f;
        
        Vector3 newPosition = transform.position;
        newPosition.x = x;
        newPosition.z = z;
        
        transform.position = newPosition;
        
        // Face movement direction
        Vector3 velocity = new Vector3(
            Mathf.Cos(demoTime) * demoRadius * demoSpeed,
            0,
            Mathf.Cos(demoTime * 2f) * demoRadius * demoSpeed
        );
        
        if (velocity.magnitude > 0.1f)
        {
            transform.rotation = Quaternion.LookRotation(velocity.normalized);
        }
    }
    
    public void ToggleSplineSystem()
    {
        if (terrainPainter != null)
        {
            terrainPainter.ToggleSmoothTurnSpline();
            UpdateButtonText();
        }
    }
    
    public void ClearTrail()
    {
        if (terrainPainter != null)
        {
            terrainPainter.ClearSplineTrail();
        }
    }
    
    public void TestSystem()
    {
        if (terrainPainter != null)
        {
            terrainPainter.TestSplineSystem();
        }
    }
    
    public void ToggleAutoDemo()
    {
        isDemoRunning = !isDemoRunning;
        demoTime = 0f;
        
        Debug.Log($"Auto Demo: {(isDemoRunning ? "Started" : "Stopped")}");
    }
    
    void UpdateButtonText()
    {
        if (toggleSplineButton != null && terrainPainter != null)
        {
            Text buttonText = toggleSplineButton.GetComponentInChildren<Text>();
            if (buttonText != null)
            {
                buttonText.text = terrainPainter.useSmoothTurnSpline ? "Disable Spline" : "Enable Spline";
            }
        }
    }
    
    void UpdateStatusText()
    {
        if (statusText != null && terrainPainter != null)
        {
            string status = $"=== FARMING SIMULATOR 2025 STYLE ===\n";
            status += $"Spline System: {(terrainPainter.useSmoothTurnSpline ? "ENABLED" : "DISABLED")}\n";
            status += $"Speed: {terrainPainter.CurrentSpeed:F1} m/s\n";
            status += $"Speed Mode: {terrainPainter.CurrentSpeedMode}\n";
            status += $"Paint Freq: {terrainPainter.CurrentPaintFrequency:F3}\n";

            if (terrainPainter.useSmoothTurnSpline)
            {
                status += $"Trail Points: {terrainPainter.TrailPoints?.Count ?? 0}\n";
                status += $"Spline Points: {terrainPainter.SmoothedSpline?.Count ?? 0}\n";
                status += $"In Turn: {(terrainPainter.IsInTurn ? "YES" : "NO")}\n";
                status += $"Turn Angle: {terrainPainter.CurrentTurnAngle:F1}°\n";
                status += $"Speed-Based: {(terrainPainter.useSpeedBasedPainting ? "ON" : "OFF")}\n";
                status += $"Adv Curves: {(terrainPainter.useAdvancedCurveSmoothing ? "ON" : "OFF")}";
            }

            statusText.text = status;
        }
    }
    
    void OnSmoothingFactorChanged(float value)
    {
        if (terrainPainter != null)
        {
            terrainPainter.smoothingFactor = value;
        }
    }
    
    void OnSplineResolutionChanged(float value)
    {
        if (terrainPainter != null)
        {
            terrainPainter.splineResolution = value;
        }
    }
    
    void OnTransitionSmoothnessChanged(float value)
    {
        if (terrainPainter != null)
        {
            terrainPainter.transitionSmoothness = value;
        }
    }
    
    public void SetPaintingSpeed(string mode)
    {
        if (terrainPainter != null)
        {
            terrainPainter.SetPaintingMode(mode);
        }
    }

    public void ToggleSpeedBasedPainting()
    {
        if (terrainPainter != null)
        {
            terrainPainter.ToggleSpeedBasedPainting();
        }
    }

    public void ToggleAdvancedCurveSmoothing()
    {
        if (terrainPainter != null)
        {
            terrainPainter.ToggleAdvancedCurveSmoothing();
        }
    }

    void ShowInstructions()
    {
        string instructions = @"
=== FARMING SIMULATOR 2025 STYLE PAINTING SYSTEM ===

KEYBOARD SHORTCUTS:
F1 - Toggle Spline System
F2 - Clear Trail
F3 - Test System Status
F4 - Toggle Auto Demo
F5 - Show Instructions
F6 - Toggle Speed-Based Painting
F7 - Toggle Advanced Curve Smoothing

SPEED CONTROL (FS25 Style):
1 - Slow Painting (High Detail)
2 - Normal Painting
3 - Fast Painting (Quick Coverage)
Ctrl + Mouse Wheel - Fine-tune frequency

FEATURES DEMONSTRATED:
✓ Catmull-Rom Spline Interpolation
✓ Real-time Turn Detection
✓ Speed-Based Paint Frequency
✓ Adaptive Brush Width/Intensity
✓ Advanced Curve Smoothing
✓ Curve Preview System
✓ Seamless Texture Transitions
✓ Edge Feathering
✓ Professional FS25-grade Results

SPEED MODES:
- SLOW: High detail, frequent painting
- NORMAL: Balanced painting
- FAST: Quick coverage, less detail

RECOMMENDED SETTINGS:
- Enable Speed-Based Painting
- Enable Advanced Curve Smoothing
- Spline Resolution: 0.2-0.4
- Smoothing Factor: 0.3-0.5
- Transition Smoothness: 0.7-0.9

Drive around to see smooth texture painting!
Speed automatically controls detail level.
Sharp turns get ultra-smooth curves.
Just like Farming Simulator 2025!
";

        Debug.Log(instructions);
    }
}
