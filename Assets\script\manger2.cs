/*
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 * ████████╗ ██████╗ ██╗   ██╗ ██████╗██╗  ██╗ █████╗ ███╗   ██╗    ███╗   ███╗ ██████╗ ██████╗     ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗
 * ╚══██╔══╝██╔═══██╗██║   ██║██╔════╝██║  ██║██╔══██╗████╗  ██║    ████╗ ████║██╔═══██╗██╔══██╗    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
 *    ██║   ██║   ██║██║   ██║██║     ███████║███████║██╔██╗ ██║    ██╔████╔██║██║   ██║██║  ██║    ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
 *    ██║   ██║   ██║██║   ██║██║     ██╔══██║██╔══██║██║╚██╗██║    ██║╚██╔╝██║██║   ██║██║  ██║    ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
 *    ██║   ╚██████╔╝╚██████╔╝╚██████╗██║  ██║██║  ██║██║ ╚████║    ██║ ╚═╝ ██║╚██████╔╝██████╔╝    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
 *    ╚═╝    ╚═════╝  ╚═════╝  ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝    ╚═╝     ╚═╝ ╚═════╝ ╚═════╝     ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * 🚛 TRACTOR SIMULATOR CARGO GAMES - TOUCHAN MODE MANAGER 🏗️
 *
 *  █████╗ ██╗     ██╗    ████████╗ █████╗      ██╗
 * ██╔══██╗██║     ██║    ╚══██╔══╝██╔══██╗     ██║
 * ███████║██║     ██║       ██║   ███████║     ██║
 * ██╔══██║██║     ██║       ██║   ██╔══██║██   ██║
 * ██║  ██║███████╗██║       ██║   ██║  ██║╚█████╔╝
 * ╚═╝  ╚═╝╚══════╝╚═╝       ╚═╝   ╚═╝  ╚═╝ ╚════╝
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📋 SCRIPT INFORMATION                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  📁 File Name      : manger2.cs                                                                                    │
 * │  🎯 Purpose        : Touchan Mode Level Management & Construction Gameplay                                         │
 * │  🏗️  Architecture   : Component-Based Design with Coroutine Management                                             │
 * │  🎨 Script Organizer: Ali Taj                                                                                       │
 * │  📅 Organized Date : 2025-07-30                                                                                     │
 * │  ⚡ Version        : V1.9 - Optimized & Beautifully Organized                                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  🌟 KEY FEATURES                                                                                                    │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🏗️  Touchan Level System      - Construction-themed level management                                              │
 * │  🚛 Vehicle Physics Control    - Rigidbody and wheel rotation management                                           │
 * │  💾 Progress Tracking          - PlayerPrefs integration for Touchan level completion                             │
 * │  🎬 Cinematic Sequences        - Intro animations and level transitions                                            │
 * │  🎵 Audio Management           - Background music and engine sound control                                         │
 * │  ⏸️  Game State Control        - Pause, resume, restart functionality                                              │
 * │  💰 Coin System Integration    - Player currency display and tracking                                              │
 * │  📱 Loading Screen Management  - Smooth scene transitions with loading panels                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📚 CORE FUNCTIONALITY                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🔹 Level Initialization      - Automatic level setup and UI updates                                               │
 * │  🔹 Vehicle Control System    - Wheel rotators and physics management                                              │
 * │  🔹 Game Flow Management      - Start sequences and instruction handling                                           │
 * │  🔹 Level Progression         - Next level advancement with progress saving                                        │
 * │  🔹 Scene Management          - Smooth transitions between Touchan levels                                          │
 * │  🔹 Audio Control             - Music and sound effect management                                                  │
 * │  🔹 UI State Management       - Panel visibility and game state control                                            │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  💡 USAGE INSTRUCTIONS                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  1️⃣  Attach this script to a GameObject in your Touchan Mode scene                                               │
 * │  2️⃣  Configure all UI panels and game objects in the Inspector                                                    │
 * │  3️⃣  Set up levels array with all Touchan level GameObjects                                                       │
 * │  4️⃣  Assign wheel transform arrays for each vehicle                                                               │
 * │  5️⃣  Configure player Rigidbody array for physics control                                                         │
 * │  6️⃣  Test level progression and vehicle physics functionality                                                     │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎨 Beautifully Organized by Ali Taj - Making Code Art! ✨
 *
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class manger2 : MonoBehaviour
{
    #region UI Elements
    [Header("UI Panels")]
    public GameObject pausepanl, startpanl, playbtn, instction;
    public GameObject loadingpanel;

    [Header("UI Text Elements")]
    public Text levlno, coins;
    #endregion

    #region Audio Elements
    [Header("Audio Components")]
    public GameObject bgmusic, engnsound;
    #endregion

    #region Game Objects Arrays
    [Header("Level Configuration")]
    public GameObject[] levels;

    [Header("Effect Arrays")]
    public GameObject[] smoke, smoke1;

    [Header("Vehicle Components")]
    public Rigidbody[] player;

    [Header("Wheel Transforms")]
    public Transform[] FL, FR, RL, RR, AIL, AIR;
    #endregion

    #region Unity Lifecycle
    void Start()
    {
        Time.timeScale = 1;
        levels[MainMenu.levlno].SetActive(true);
        levlno.text = "Level : " + (MainMenu.levlno + 1);
        coins.text = PlayerPrefs.GetInt("coins").ToString();

        StartCoroutine(S());
    }
    #endregion

    #region UI Control Methods
    public void pause()
    {
        pausepanl.SetActive(true);
        AdsController.Instance.ShowInterstitialAd_Admob();
        AudioListener.volume = 0f;
    }

    public void restart()
    {
        AudioListener.volume = 1f;
        StartCoroutine(R());
    }

    public void resume()
    {
        pausepanl.SetActive(false);
        AudioListener.volume = 1f;
    }

    public void home()
    {
        AudioListener.volume = 1f;
        StartCoroutine("H");
    }

    public void next()
    {
        MainMenu.CompleteTouchanLevel(MainMenu.levlno);
        StartCoroutine("strt");
    }
    #endregion

    #region Game Flow Coroutines
    IEnumerator R()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("tractortochan");
    }

    IEnumerator strt()
    {
        loadingpanel.SetActive(true);
        yield return new WaitForSeconds(4f);
        if (MainMenu.levlno < levels.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Touchan" + MainMenu.levlno, 1);
            PlayerPrefs.Save();
            SceneManager.LoadScene("tractortochan");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }

    IEnumerator H()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("MainMenu");
    }
    #endregion

    #region Level Initialization Coroutines
    IEnumerator start()
    {
        yield return new WaitForSeconds(3f);
        startpanl.SetActive(false);
        smoke[MainMenu.levlno].SetActive(true);
        smoke1[MainMenu.levlno].SetActive(true);
        playbtn.SetActive(true);
        player[MainMenu.levlno].GetComponent<Rigidbody>().isKinematic = false;
        bgmusic.SetActive(true);
        engnsound.SetActive(true);
        FL[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        FR[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        RL[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        RR[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        AIL[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        AIR[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
    }

    IEnumerator obect()
    {
        yield return new WaitForSeconds(5f);
        instction.SetActive(false);
        startpanl.SetActive(true);
        StartCoroutine(S());
    }

    IEnumerator S()
    {
        yield return new WaitForSeconds(1f);
        if (PlayerPrefs.GetInt("start") == 0)
        {
            instction.SetActive(true);
            startpanl.SetActive(false);
            PlayerPrefs.SetInt("start", 1);
            StartCoroutine("obect");
        }
        else
        {
            instction.SetActive(false);
            startpanl.SetActive(true);
            StartCoroutine("start");
        }
    }
    #endregion
}

