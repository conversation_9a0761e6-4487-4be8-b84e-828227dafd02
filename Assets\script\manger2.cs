using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class manger2 : MonoBehaviour
{
    public GameObject pausepanl, startpanl, playbtn, bgmusic, engnsound, instction;
    public GameObject[] smoke, smoke1, levels;
    public Transform[] FL, FR, RL, RR, AIL, AIR;
    public Rigidbody[] player;
    public Text levlno, coins;
    void Start()
    {
        Time.timeScale = 1;
        levels[MainMenu.levlno].SetActive(true);
        levlno.text = "Level : " + (MainMenu.levlno + 1);
        coins.text = PlayerPrefs.GetInt("coins").ToString();

        StartCoroutine(S());
    }
    public void pause()
    {
        pausepanl.SetActive(true);
        AdsController.Instance.ShowInterstitialAd_Admob();
        AudioListener.volume = 0f;

    }
    public void restart()
    {

        AudioListener.volume = 1f;
        StartCoroutine(R());
    }
    IEnumerator R()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("tractortochan");
    }

    public void next()
    {

        MainMenu.CompleteTouchanLevel(MainMenu.levlno);
        StartCoroutine("strt");
    }
    public GameObject loadingpanel;
    IEnumerator strt()
    {
        loadingpanel.SetActive(true);
        yield return new WaitForSeconds(4f);
        if (MainMenu.levlno < levels.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Touchan" + MainMenu.levlno, 1);
            PlayerPrefs.Save();
            SceneManager.LoadScene("tractortochan");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }
    public void resume()
    {
        pausepanl.SetActive(false);
        AudioListener.volume = 1f;

    }
    public void home()
    {

        AudioListener.volume = 1f;
        StartCoroutine("H");

    }
    IEnumerator H()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("MainMenu");
    }
    IEnumerator start()
    {
        yield return new WaitForSeconds(3f);
        startpanl.SetActive(false);
        smoke[MainMenu.levlno].SetActive(true);
        smoke1[MainMenu.levlno].SetActive(true);
        playbtn.SetActive(true);
        player[MainMenu.levlno].GetComponent<Rigidbody>().isKinematic = false;
        bgmusic.SetActive(true);
        engnsound.SetActive(true);
        FL[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        FR[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        RL[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        RR[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        AIL[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
        AIR[MainMenu.levlno].GetComponent<wheelrotator>().enabled = true;
    }
    IEnumerator obect()
    {
        yield return new WaitForSeconds(5f);
        instction.SetActive(false);
        startpanl.SetActive(true);
        StartCoroutine(S());
    }
    IEnumerator S()
    {
        yield return new WaitForSeconds(1f);
        if (PlayerPrefs.GetInt("start") == 0)
        {
            instction.SetActive(true);
            startpanl.SetActive(false);
            PlayerPrefs.SetInt("start", 1);
            StartCoroutine("obect");
        }
        else
        {
            instction.SetActive(false);
            startpanl.SetActive(true);
            StartCoroutine("start");
        }
    }
}

