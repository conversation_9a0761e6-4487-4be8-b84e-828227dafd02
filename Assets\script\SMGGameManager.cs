using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class SMGGameManager : MonoBehaviour
{
    public GameObject Loading;
    public static SMGGameManager Instance;
    public GameObject player, objectpnl, trolly, completpnl, pasupanl, gamplybtn, sheep, door, door1, effect, rcccanvec, harvestr, harvestr1, lvlcam8, bpnl,
       rccacm, men, wypont, instruction, rope, newman, driver, newman1, house, asset, ylotrctor, Home, chekpoint, corn;
    public GameObject[] startpositn, Levels, trolypositn, woods, levlcam;
    public Text levltext, levlno, coins, L8text;
    public Button musicplay, musicstop;
    public AudioSource[] musics;
    public string[] levlmasg;
    public string levlmasg1;
    int randomnumber;
    public Texture[] yourtex;
    public Material tractor;
    int i, clickCounter;
    public RCC_Camera cam;
    void Start()
    {
        Time.timeScale = 1;
        AudioListener.volume = 1f;
        Instance = this;
        Application.targetFrameRate = 0;
        if (MainMenu.levlno == 0)
        {
            rccacm.SetActive(false);
            driver.SetActive(false);
            levlcam[MainMenu.levlno].SetActive(true);
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(false);
            StartCoroutine("lvl0");
        }
        else if (MainMenu.levlno == 6)
        {
            door.SetActive(false);
            door1.SetActive(true);
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(false);
            woods[MainMenu.levlno].SetActive(false);
            levlcam[MainMenu.levlno].SetActive(true);
            rccacm.SetActive(false);
            StartCoroutine("lvl1");
        }
        else if (MainMenu.levlno == 3)
        {
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(false);
            woods[MainMenu.levlno].SetActive(false);
            levlcam[MainMenu.levlno].SetActive(true);
            rccacm.SetActive(false);
            StartCoroutine("lvl2");
        }
        else if (MainMenu.levlno == 4)
        {
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(false);
            house.SetActive(false);
            rccacm.SetActive(false);
            levlcam[MainMenu.levlno].SetActive(true);
            StartCoroutine("lvl5");
        }
        else if (MainMenu.levlno == 5)
        {
            asset.SetActive(false);
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(true);
            woods[MainMenu.levlno].SetActive(true);
        }
        else if (MainMenu.levlno == 2 || MainMenu.levlno == 8)
        {
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(true);
            woods[MainMenu.levlno].SetActive(true);
            RCC_Camera cam = RCC_SceneManager.Instance.activePlayerCamera;
            cam.TPSDistance = 22f;
            cam.TPSHeight = 7f;
        }
        else if (MainMenu.levlno == 7)
        {
            cam.TPSDistance = 27;
            Home.SetActive(false);
            corn.SetActive(false);
            rope.SetActive(true);
            player.SetActive(false);
            woods[MainMenu.levlno].SetActive(false);
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(false);
            levlcam[MainMenu.levlno].SetActive(true);
            StartCoroutine("lvl7");
        }
        else
        {
            levltext.text = "" + levlmasg[MainMenu.levlno];
            objectpnl.SetActive(true);
            woods[MainMenu.levlno].SetActive(true);
        }
        levlno.text = "Level : " + (MainMenu.levlno + 1);
        Levels[MainMenu.levlno].SetActive(true);
        player.transform.position = startpositn[MainMenu.levlno].transform.position;
        player.transform.rotation = startpositn[MainMenu.levlno].transform.rotation;
        trolly.transform.position = trolypositn[MainMenu.levlno].transform.position;
        trolly.transform.rotation = trolypositn[MainMenu.levlno].transform.rotation;
        {
            randomnumber = Random.Range(0, yourtex.Length);
            tractor.mainTexture = yourtex[randomnumber];
        }

    }
    void Update()
    {
        coins.text = PlayerPrefs.GetInt("coins").ToString();
    }
    public void gameplayrestart()
    {
        SceneManager.LoadScene("gameplay");
    }
    public void restart()
    {

        AudioListener.volume = 1f;
        StartCoroutine(R());

    }
    IEnumerator R()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("gameplay");

    }
    public void pause()
    {
        pasupanl.SetActive(true);
        AdsController.Instance.ShowInterstitialAd_Admob();
        AudioListener.volume = 0f;

    }
    public void ok()
    {

        objectpnl.SetActive(false);
    }
    public void home()
    {
        AudioListener.volume = 1f;

        StartCoroutine(h());
    }
    IEnumerator h()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("MainMenu");
    }
    public void resume()
    {
        pasupanl.SetActive(false);
        AudioListener.volume = 1f;

    }
    public void next()
    {
        MainMenu.CompleteCareerLevel(MainMenu.levlno);
        StartCoroutine(N());
    }
    IEnumerator N()
    {
        yield return new WaitForSeconds(4);
        if (MainMenu.levlno < Levels.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Career" + MainMenu.levlno, 1);
            PlayerPrefs.Save();

            SceneManager.LoadScene("gameplay");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }
    public void Steer()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }
    public void Btns()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }
    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    IEnumerator lvl1()
    {
        yield return new WaitForSeconds(10f);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
        sheep.SetActive(false);
        woods[MainMenu.levlno].SetActive(true);
        door.SetActive(true);
        door1.SetActive(false);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
    }
    IEnumerator lvl2()
    {
        yield return new WaitForSeconds(0.5f);
        effect.SetActive(true);
        yield return new WaitForSeconds(18f);
        effect.SetActive(false);
        harvestr.SetActive(false);
        harvestr1.SetActive(true);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
    }
    IEnumerator lvl5()
    {
        yield return new WaitForSeconds(4f);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        rccacm.SetActive(true);
        levlcam[MainMenu.levlno].SetActive(false);
    }
    IEnumerator lvl7()
    {
        yield return new WaitForSeconds(14f);
        bpnl.SetActive(true);
        yield return new WaitForSeconds(1f);
        men.SetActive(true);
        wypont.SetActive(false);
        levlcam[MainMenu.levlno].SetActive(false);
        lvlcam8.SetActive(true);
        yield return new WaitForSeconds(1f);
        bpnl.SetActive(false);
        L8text.text = "" + levlmasg1;
        instruction.SetActive(true);
        yield return new WaitForSeconds(16f);
        instruction.SetActive(false);
        ylotrctor.SetActive(false);
        lvlcam8.SetActive(false);
        player.SetActive(true);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        chekpoint.SetActive(true);
    }
    IEnumerator lvl0()
    {
        yield return new WaitForSeconds(9f);
        newman1.SetActive(false);
        newman.SetActive(true);
        yield return new WaitForSeconds(5f);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
        bpnl.SetActive(true);
        yield return new WaitForSeconds(1f);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
        newman.SetActive(false);
        driver.SetActive(true);
        bpnl.SetActive(false);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
    }
    public void changcontrol()
    {
        clickCounter++;
        switch (clickCounter)
        {
            case 1:
                RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
                break;
            case 2:
                RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
                clickCounter = 0;
                break;
        }
    }

    public void PlayMusic()
    {

        foreach (AudioSource music in musics)
        {
            music.Play();
        }

        musicplay.gameObject.SetActive(false);
        musicstop.gameObject.SetActive(true);
    }

    public void StopMusic()
    {

        foreach (AudioSource music in musics)
        {
            music.Pause();
        }

        musicplay.gameObject.SetActive(true);
        musicstop.gameObject.SetActive(false);
    }
}
