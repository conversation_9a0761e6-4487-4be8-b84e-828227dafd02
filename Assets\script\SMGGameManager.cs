/*
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 * ██████╗ ███╗   ███╗ ██████╗      ██████╗  █████╗ ███╗   ███╗███████╗    ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗
 * ██╔════╝ ████╗ ████║██╔════╝     ██╔════╝ ██╔══██╗████╗ ████║██╔════╝    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
 * ███████╗ ██╔████╔██║██║  ███╗    ██║  ███╗███████║██╔████╔██║█████╗      ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
 * ╚════██║ ██║╚██╔╝██║██║   ██║    ██║   ██║██╔══██║██║╚██╔╝██║██╔══╝      ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
 * ███████║ ██║ ╚═╝ ██║╚██████╔╝    ╚██████╔╝██║  ██║██║ ╚═╝ ██║███████╗    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
 * ╚══════╝ ╚═╝     ╚═╝ ╚═════╝      ╚═════╝ ╚═╝  ╚═╝╚═╝     ╚═╝╚══════╝    ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * 🎮 TRACTOR SIMULATOR CARGO GAMES - CAREER MODE MANAGER 🚜
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📋 SCRIPT INFORMATION                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  📁 File Name      : SMGGameManager.cs                                                                             │
 * │  🎯 Purpose        : Main Game Manager for Career Mode Levels                                                      │
 * │  🏗️  Architecture   : Singleton Pattern with Organized Regions                                                     │
 * │  🎨 Script Organizer: Ali Taj                                                                                       │
 * │  📅 Organized Date : 2025-07-30                                                                                     │
 * │  ⚡ Version        : V1.9 - Optimized & Beautifully Organized                                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  🌟 KEY FEATURES                                                                                                    │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  ✨ Level Management System    - Dynamic level loading and progression                                             │
 * │  🎮 Control System Integration - Multiple input methods (Touch, Steering, Gyro)                                   │
 * │  🎵 Audio Management          - Background music and sound effects control                                         │
 * │  💾 Progress Saving           - PlayerPrefs integration for level completion                                       │
 * │  🎬 Cinematic Sequences       - Level-specific intro animations and cutscenes                                      │
 * │  🎨 UI Management             - Comprehensive UI panel and element control                                         │
 * │  🚜 Vehicle Positioning       - Dynamic player and trolley placement system                                        │
 * │  🎲 Randomization             - Random tractor texture selection                                                   │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📚 ORGANIZATION STRUCTURE                                                                                          │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🔹 Singleton                 - Instance management                                                                 │
 * │  🔹 UI Elements              - Panels, texts, buttons with headers                                                 │
 * │  🔹 Game Objects             - Player, vehicles, environment objects                                               │
 * │  🔹 Arrays                   - Level configurations and asset arrays                                               │
 * │  🔹 Unity Lifecycle          - Start, Update methods                                                               │
 * │  🔹 Initialization Methods   - Game setup and configuration                                                        │
 * │  🔹 Level Setup Methods      - Individual level configurations                                                     │
 * │  🔹 UI Control Methods       - User interface interactions                                                         │
 * │  🔹 Level Progression        - Career advancement system                                                           │
 * │  🔹 Control Settings         - Input method configurations                                                         │
 * │  🔹 Level-Specific Coroutines- Cinematic sequences for each level                                                  │
 * │  🔹 Audio Control Methods    - Music and sound management                                                          │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  💡 USAGE INSTRUCTIONS                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  1️⃣  Attach this script to a GameObject in your Career Mode scene                                                 │
 * │  2️⃣  Configure all public variables in the Inspector                                                              │
 * │  3️⃣  Ensure all UI elements and game objects are properly assigned                                                │
 * │  4️⃣  Set up level arrays with appropriate configurations                                                          │
 * │  5️⃣  Test each level's specific setup and coroutine sequences                                                     │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎨 Beautifully Organized by Ali Taj - Making Code Art! ✨
 *
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class SMGGameManager : MonoBehaviour
{
    #region Singleton
    public static SMGGameManager Instance;
    #endregion

    #region UI Elements
    [Header("Loading & UI Panels")]
    public GameObject Loading;
    public GameObject objectpnl;
    public GameObject completpnl;
    public GameObject pasupanl;
    public GameObject gamplybtn;
    public GameObject effect;
    public GameObject bpnl;
    public GameObject rcccanvec;
    public GameObject instruction;
    public GameObject chekpoint;

    [Header("UI Text Elements")]
    public Text levltext;
    public Text levlno;
    public Text coins;
    public Text L8text;

    [Header("Audio Controls")]
    public Button musicplay;
    public Button musicstop;
    public AudioSource[] musics;
    #endregion

    #region Game Objects
    [Header("Player & Vehicles")]
    public GameObject player;
    public GameObject trolly;
    public GameObject ylotrctor;
    public RCC_Camera cam;
    public GameObject rccacm;

    [Header("Environment Objects")]
    public GameObject sheep;
    public GameObject door;
    public GameObject door1;
    public GameObject harvestr;
    public GameObject harvestr1;
    public GameObject lvlcam8;
    public GameObject men;
    public GameObject wypont;
    public GameObject rope;
    public GameObject newman;
    public GameObject driver;
    public GameObject newman1;
    public GameObject house;
    public GameObject asset;
    public GameObject Home;
    public GameObject corn;
    #endregion

    #region Arrays
    [Header("Level Arrays")]
    public GameObject[] startpositn;
    public GameObject[] Levels;
    public GameObject[] trolypositn;
    public GameObject[] woods;
    public GameObject[] levlcam;

    [Header("Level Configuration")]
    public string[] levlmasg;
    public Texture[] yourtex;
    #endregion

    #region Private Variables
    private string levlmasg1;
    private int randomnumber;
    private Material tractor;
    private int i, clickCounter;
    #endregion

    #region Unity Lifecycle
    void Start()
    {
        InitializeGame();
        InitializeLevelSpecificSettings();
        SetupPlayerAndTrolley();
        ApplyRandomTractorTexture();
    }

    void Update()
    {
        UpdateUI();
    }
    #endregion

    #region Initialization Methods
    private void InitializeGame()
    {
        Time.timeScale = 1;
        AudioListener.volume = 1f;
        Instance = this;
        Application.targetFrameRate = 0;

        levlno.text = "Level : " + (MainMenu.levlno + 1);
        Levels[MainMenu.levlno].SetActive(true);
    }

    private void InitializeLevelSpecificSettings()
    {
        switch (MainMenu.levlno)
        {
            case 0:
                SetupLevel0();
                break;
            case 2:
            case 8:
                SetupLevel2And8();
                break;
            case 3:
                SetupLevel3();
                break;
            case 4:
                SetupLevel4();
                break;
            case 5:
                SetupLevel5();
                break;
            case 6:
                SetupLevel6();
                break;
            case 7:
                SetupLevel7();
                break;
            default:
                SetupDefaultLevel();
                break;
        }
    }

    private void SetupPlayerAndTrolley()
    {
        player.transform.position = startpositn[MainMenu.levlno].transform.position;
        player.transform.rotation = startpositn[MainMenu.levlno].transform.rotation;
        trolly.transform.position = trolypositn[MainMenu.levlno].transform.position;
        trolly.transform.rotation = trolypositn[MainMenu.levlno].transform.rotation;
    }

    private void ApplyRandomTractorTexture()
    {
        randomnumber = Random.Range(0, yourtex.Length);
        tractor.mainTexture = yourtex[randomnumber];
    }

    private void UpdateUI()
    {
        coins.text = PlayerPrefs.GetInt("coins").ToString();
    }
    #endregion

    #region Level Setup Methods
    private void SetupLevel0()
    {
        rccacm.SetActive(false);
        driver.SetActive(false);
        levlcam[MainMenu.levlno].SetActive(true);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(false);
        StartCoroutine("lvl0");
    }

    private void SetupLevel2And8()
    {
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        RCC_Camera cam = RCC_SceneManager.Instance.activePlayerCamera;
        cam.TPSDistance = 22f;
        cam.TPSHeight = 7f;
    }

    private void SetupLevel3()
    {
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(false);
        woods[MainMenu.levlno].SetActive(false);
        levlcam[MainMenu.levlno].SetActive(true);
        rccacm.SetActive(false);
        StartCoroutine("lvl2");
    }

    private void SetupLevel4()
    {
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(false);
        house.SetActive(false);
        rccacm.SetActive(false);
        levlcam[MainMenu.levlno].SetActive(true);
        StartCoroutine("lvl5");
    }

    private void SetupLevel5()
    {
        asset.SetActive(false);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
    }

    private void SetupLevel6()
    {
        door.SetActive(false);
        door1.SetActive(true);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(false);
        woods[MainMenu.levlno].SetActive(false);
        levlcam[MainMenu.levlno].SetActive(true);
        rccacm.SetActive(false);
        StartCoroutine("lvl1");
    }

    private void SetupLevel7()
    {
        cam.TPSDistance = 27;
        Home.SetActive(false);
        corn.SetActive(false);
        rope.SetActive(true);
        player.SetActive(false);
        woods[MainMenu.levlno].SetActive(false);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(false);
        levlcam[MainMenu.levlno].SetActive(true);
        StartCoroutine("lvl7");
    }

    private void SetupDefaultLevel()
    {
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
    }
    #endregion

    #region UI Control Methods
    /// <summary>
    /// Restart the current gameplay scene immediately
    /// </summary>
    public void gameplayrestart()
    {
        SceneManager.LoadScene("gameplay");
    }

    /// <summary>
    /// Restart the game with loading delay
    /// </summary>
    public void restart()
    {
        AudioListener.volume = 1f;
        StartCoroutine(RestartCoroutine());
    }

    /// <summary>
    /// Pause the game and show ads
    /// </summary>
    public void pause()
    {
        pasupanl.SetActive(true);
        AdsController.Instance.ShowInterstitialAd_Admob();
        AudioListener.volume = 0f;
    }

    /// <summary>
    /// Resume the game from pause
    /// </summary>
    public void resume()
    {
        pasupanl.SetActive(false);
        AudioListener.volume = 1f;
    }

    /// <summary>
    /// Close the objective panel
    /// </summary>
    public void ok()
    {
        objectpnl.SetActive(false);
    }

    /// <summary>
    /// Return to main menu with loading delay
    /// </summary>
    public void home()
    {
        AudioListener.volume = 1f;
        StartCoroutine(HomeCoroutine());
    }
    #endregion

    #region Game Flow Coroutines
    private IEnumerator RestartCoroutine()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("gameplay");
    }

    private IEnumerator HomeCoroutine()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("MainMenu");
    }
    #endregion

    #region Level Progression
    /// <summary>
    /// Proceed to the next career level
    /// </summary>
    public void next()
    {
        MainMenu.CompleteCareerLevel(MainMenu.levlno);
        StartCoroutine(NextLevelCoroutine());
    }

    private IEnumerator NextLevelCoroutine()
    {
        yield return new WaitForSeconds(4);
        if (MainMenu.levlno < Levels.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Career" + MainMenu.levlno, 1);
            PlayerPrefs.Save();
            SceneManager.LoadScene("gameplay");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }
    #endregion

    #region Control Settings
    /// <summary>
    /// Set steering wheel control mode
    /// </summary>
    public void Steer()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }

    /// <summary>
    /// Set touch button control mode
    /// </summary>
    public void Btns()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }

    /// <summary>
    /// Set gyroscope/tilt control mode
    /// </summary>
    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    #endregion

    #region Level-Specific Coroutines
    /// <summary>
    /// Level 0 sequence coroutine
    /// </summary>
    private IEnumerator lvl0()
    {
        yield return new WaitForSeconds(9f);
        newman1.SetActive(false);
        newman.SetActive(true);
        yield return new WaitForSeconds(5f);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
        bpnl.SetActive(true);
        yield return new WaitForSeconds(1f);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
        newman.SetActive(false);
        driver.SetActive(true);
        bpnl.SetActive(false);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
    }

    /// <summary>
    /// Level 1 sequence coroutine (Level 6 setup)
    /// </summary>
    private IEnumerator lvl1()
    {
        yield return new WaitForSeconds(10f);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
        sheep.SetActive(false);
        woods[MainMenu.levlno].SetActive(true);
        door.SetActive(true);
        door1.SetActive(false);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
    }

    /// <summary>
    /// Level 2 sequence coroutine (Level 3 setup)
    /// </summary>
    private IEnumerator lvl2()
    {
        yield return new WaitForSeconds(0.5f);
        effect.SetActive(true);
        yield return new WaitForSeconds(18f);
        effect.SetActive(false);
        harvestr.SetActive(false);
        harvestr1.SetActive(true);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        levlcam[MainMenu.levlno].SetActive(false);
        rccacm.SetActive(true);
    }

    /// <summary>
    /// Level 5 sequence coroutine (Level 4 setup)
    /// </summary>
    private IEnumerator lvl5()
    {
        yield return new WaitForSeconds(4f);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        rccacm.SetActive(true);
        levlcam[MainMenu.levlno].SetActive(false);
    }

    /// <summary>
    /// Level 7 sequence coroutine
    /// </summary>
    private IEnumerator lvl7()
    {
        yield return new WaitForSeconds(14f);
        bpnl.SetActive(true);
        yield return new WaitForSeconds(1f);
        men.SetActive(true);
        wypont.SetActive(false);
        levlcam[MainMenu.levlno].SetActive(false);
        lvlcam8.SetActive(true);
        yield return new WaitForSeconds(1f);
        bpnl.SetActive(false);
        L8text.text = "" + levlmasg1;
        instruction.SetActive(true);
        yield return new WaitForSeconds(16f);
        instruction.SetActive(false);
        ylotrctor.SetActive(false);
        lvlcam8.SetActive(false);
        player.SetActive(true);
        levltext.text = "" + levlmasg[MainMenu.levlno];
        objectpnl.SetActive(true);
        woods[MainMenu.levlno].SetActive(true);
        chekpoint.SetActive(true);
    }
    #endregion

    #region Additional Control Methods
    /// <summary>
    /// Toggle between different control schemes
    /// </summary>
    public void changcontrol()
    {
        clickCounter++;
        switch (clickCounter)
        {
            case 1:
                RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
                break;
            case 2:
                RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
                clickCounter = 0;
                break;
        }
    }
    #endregion

    #region Audio Control Methods
    /// <summary>
    /// Play all background music
    /// </summary>
    public void PlayMusic()
    {
        foreach (AudioSource music in musics)
        {
            music.Play();
        }
        musicplay.gameObject.SetActive(false);
        musicstop.gameObject.SetActive(true);
    }

    /// <summary>
    /// Stop all background music
    /// </summary>
    public void StopMusic()
    {
        foreach (AudioSource music in musics)
        {
            music.Pause();
        }
        musicplay.gameObject.SetActive(true);
        musicstop.gameObject.SetActive(false);
    }
    #endregion
}
