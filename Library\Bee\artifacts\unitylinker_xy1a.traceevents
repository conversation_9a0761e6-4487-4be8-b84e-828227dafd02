{ "pid": "host", "ph":"M", "name": "process_name", "args": {"name": "host"} },
{ "pid": "host", "ph":"M", "name": "process_sort_index", "args": {"sort_index": 0} },
{ "pid": "host", "tid": 1, "ph":"M", "name": "thread_name", "args": {"name": ""} },
{ "pid": "host", "tid": 1,"ts": 1753846787154886,"dur": 8712174, "ph":"X", "name": "UnityLinker.exe"},
{ "pid": "host", "tid": 1,"ts": 1753846787155988,"dur": 167116, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityEngineSteps"},
{ "pid": "host", "tid": 1,"ts": 1753846787323158,"dur": 470903, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveUnityEngine"},
{ "pid": "host", "tid": 1,"ts": 1753846787794807,"dur": 300368, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\StagingArea\\Data\\Managed\\MethodsToPreserve.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788095203,"dur": 591492, "ph":"X", "name": "Step : Unity.Linker.Steps.InitializeEngineStrippingStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788686712,"dur": 6208, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveForEngineModuleStrippingEnabledStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788692939,"dur": 14922, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\StagingArea\\Data\\Managed\\TypesInScenes.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788707870,"dur": 1232, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\StagingArea\\Data\\Managed\\SerializedTypes.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788709105,"dur": 5269, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\InputSystemLink.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788714378,"dur": 5130, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assets\\GoogleMobileAds\\link.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788720120,"dur": 17922, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveAssemblyDirectoryStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788738056,"dur": 12271, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupAndRegisterUnityRootsSteps"},
{ "pid": "host", "tid": 1,"ts": 1753846788750343,"dur": 8388, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788758739,"dur": 6600, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788765344,"dur": 1157, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788766503,"dur": 3139, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788769645,"dur": 34915, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788804576,"dur": 1783, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788806361,"dur": 1694, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788808057,"dur": 1446, "ph":"X", "name": "Step : Mono.Linker.Steps.ResolveFromAssemblyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788809622,"dur": 2798, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityLoadReferencesStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788813282,"dur": 1519, "ph":"X", "name": "Step : Unity.Linker.Steps.SetupI18N"},
{ "pid": "host", "tid": 1,"ts": 1753846788814811,"dur": 53793, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.ResolveFromDescriptorsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788869178,"dur": 2688, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788871870,"dur": 1306, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\45\\System.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788873181,"dur": 1181, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\il2cpp\\LinkerDescriptors\\Default\\45\\mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788874374,"dur": 20316, "ph":"X", "name": "Step : Unity.Linker.Steps.Resolution.UnityBlacklistStep"},
{ "pid": "host", "tid": 1,"ts": 1753846788894696,"dur": 6628, "ph":"X", "name": "Step : UnityUnityResolveFromXmlStep: mscorlib Resource: mscorlib.xml"},
{ "pid": "host", "tid": 1,"ts": 1753846788901338,"dur": 520660, "ph":"X", "name": "Step : Mono.Linker.Steps.DynamicDependencyLookupStep"},
{ "pid": "host", "tid": 1,"ts": 1753846789422917,"dur": 1175, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveTestsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846789424614,"dur": 1590, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveMonoBehaviourItselfStep"},
{ "pid": "host", "tid": 1,"ts": 1753846789426220,"dur": 1341, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromAllUserMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1753846789427572,"dur": 8994, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromMonoBehaviours"},
{ "pid": "host", "tid": 1,"ts": 1753846789439189,"dur": 321905, "ph":"X", "name": "Step : Unity.Linker.Steps.Rooting.ResolveFromPreserveAttribute"},
{ "pid": "host", "tid": 1,"ts": 1753846789761126,"dur": 18320, "ph":"X", "name": "Step : Unity.Linker.Steps.EngineStrippingAnnotationStep"},
{ "pid": "host", "tid": 1,"ts": 1753846789779460,"dur": 309860, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityTypeMapStep"},
{ "pid": "host", "tid": 1,"ts": 1753846790089795,"dur": 417061, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeMarkAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846790506887,"dur": 34302, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveSecurityStep"},
{ "pid": "host", "tid": 1,"ts": 1753846790541208,"dur": 7423, "ph":"X", "name": "Step : Unity.Linker.Steps.RemoveSecurityFromCopyAssemblies"},
{ "pid": "host", "tid": 1,"ts": 1753846790548643,"dur": 23777, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveFeaturesStep"},
{ "pid": "host", "tid": 1,"ts": 1753846790572439,"dur": 28833, "ph":"X", "name": "Step : Mono.Linker.Steps.RemoveUnreachableBlocksStep"},
{ "pid": "host", "tid": 1,"ts": 1753846790601291,"dur": 3427885, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityMarkStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794029193,"dur": 1202, "ph":"X", "name": "Step : Mono.Linker.Steps.ValidateVirtualMethodAnnotationsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794030408,"dur": 10137, "ph":"X", "name": "Step : Mono.Linker.Steps.ProcessWarningsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794040557,"dur": 300612, "ph":"X", "name": "Step : Unity.Linker.Steps.UnitySweepStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794341184,"dur": 5306, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityCodeRewriterStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794346502,"dur": 6924, "ph":"X", "name": "Step : Mono.Linker.Steps.CleanStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794353435,"dur": 4956, "ph":"X", "name": "Step : Unity.Linker.Steps.StubifyStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794358400,"dur": 4254, "ph":"X", "name": "Step : Unity.Linker.Steps.AddUnresolvedStubsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794362662,"dur": 5694, "ph":"X", "name": "Step : Unity.Linker.Steps.Analytics.BeforeOutputAnalyticsStep"},
{ "pid": "host", "tid": 1,"ts": 1753846794370100,"dur": 1324826, "ph":"X", "name": "Step : Unity.Linker.Steps.UnityOutputStep"},
{ "pid": "host", "tid": 1,"ts": 1753846795694941,"dur": 55208, "ph":"X", "name": "Step : Unity.Linker.Steps.LinkerToEditorDataGenerationStep"},
{ "pid": "host", "tid": 1,"ts": 1753846795763815,"dur": 103243, "ph":"X", "name": "Analytics"},
{ "pid": "host", "tid": 0, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": 0,"ts": 1753846787845000,"dur": 8000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846788710000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846788778000,"dur": 14000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846788943000,"dur": 12000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846789066000,"dur": 15000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846789304000,"dur": 20000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846789533000,"dur": 58000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846789878000,"dur": 51000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790130000,"dur": 56000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790339000,"dur": 35000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790489000,"dur": 0, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790572000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790847000,"dur": 9000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790893000,"dur": 3000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846790982000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846791017000,"dur": 2000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846791097000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846791153000,"dur": 4000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846791199000,"dur": 1000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846791332000,"dur": 3000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846791799000,"dur": 13000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846792081000,"dur": 28000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846792410000,"dur": 30000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846793283000,"dur": 14000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846793658000,"dur": 17000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846794308000,"dur": 39000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846795221000,"dur": 20000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": 0,"ts": 1753846795598000,"dur": 22000, "ph":"X", "name": "GC - Gen 0", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -1,"ts": 1753846788663000,"dur": 10000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846788993000,"dur": 39000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846789122000,"dur": 47000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846789367000,"dur": 56000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846789628000,"dur": 130000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846789997000,"dur": 39000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846790406000,"dur": 61000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846790932000,"dur": 12000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846791055000,"dur": 2000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846791282000,"dur": 8000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846791646000,"dur": 25000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846791942000,"dur": 31000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846792248000,"dur": 29000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846792554000,"dur": 29000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846792762000,"dur": 52000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846792932000,"dur": 30000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846793093000,"dur": 40000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846793449000,"dur": 88000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846793827000,"dur": 89000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846794804000,"dur": 32000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846795003000,"dur": 58000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -1,"ts": 1753846795358000,"dur": 45000, "ph":"X", "name": "GC - Gen 1", "args": { "detail": "Reason: SOH allocation, Type: Blocking GC"}
},
{ "pid": "host", "tid": -2, "ph":"M", "name": "thread_name", "args": {"name": "GC"} },
{ "pid": "host", "tid": -2,"ts": 1753846787275000,"dur": 5000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: LOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753846787845000,"dur": 17000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753846788778000,"dur": 27000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753846789122000,"dur": 144000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753846790130000,"dur": 167000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
{ "pid": "host", "tid": -2,"ts": 1753846793827000,"dur": 461000, "ph":"X", "name": "GC - Gen 2", "args": { "detail": "Reason: SOH allocation, Type: Background GC"}
},
