Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker2.log
-srvPort
61808
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 147.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56812
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.011760 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 454 ms
Refreshing native plugins compatible for Editor in 120.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.544 seconds
Domain Reload Profiling:
	ReloadAssembly (1544ms)
		BeginReloadAssembly (178ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1208ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (174ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (45ms)
			SetupLoadedEditorAssemblies (923ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (570ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (120ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (164ms)
				ProcessInitializeOnLoadMethodAttributes (67ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.018632 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 117.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.697 seconds
Domain Reload Profiling:
	ReloadAssembly (2698ms)
		BeginReloadAssembly (246ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (2252ms)
			LoadAssemblies (187ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (379ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (120ms)
			SetupLoadedEditorAssemblies (1560ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (118ms)
				BeforeProcessingInitializeOnLoad (109ms)
				ProcessInitializeOnLoadAttributes (1207ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3733 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4157.
Memory consumption went from 249.2 MB to 248.5 MB.
Total: 7.657000 ms (FindLiveObjects: 0.449200 ms CreateObjectMapping: 0.242200 ms MarkObjects: 6.149100 ms  DeleteObjects: 0.815500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 173980.858151 seconds.
  path: Assets/RealisticCarControllerV3/Lens Flares/FlareSmall.flare
  artifactKey: Guid(a090713097bad9c4ab6f417254bf4e19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Lens Flares/FlareSmall.flare using Guid(a090713097bad9c4ab6f417254bf4e19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dc62b4548fb2b19058661396a3058352') in 0.106637 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/GameMy/Water_Fountain/WaterEffects/Prefabs/WaterfallSmallEffect.prefab
  artifactKey: Guid(e0ea35e989dfcd043b42d7053b546abc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/Water_Fountain/WaterEffects/Prefabs/WaterfallSmallEffect.prefab using Guid(e0ea35e989dfcd043b42d7053b546abc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3459a0248783bdc04f78ff254771dc2b') in 0.280909 seconds 
Number of asset objects unloaded after import = 41
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.023365 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  3.084 seconds
Domain Reload Profiling:
	ReloadAssembly (3085ms)
		BeginReloadAssembly (755ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (14ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (269ms)
		EndReloadAssembly (2140ms)
			LoadAssemblies (271ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (420ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1410ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1184ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4181.
Memory consumption went from 249.8 MB to 249.1 MB.
Total: 8.020700 ms (FindLiveObjects: 0.630600 ms CreateObjectMapping: 0.370600 ms MarkObjects: 6.235600 ms  DeleteObjects: 0.782400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014426 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.443 seconds
Domain Reload Profiling:
	ReloadAssembly (2444ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2041ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (354ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1409ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1174ms)
				ProcessInitializeOnLoadMethodAttributes (99ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4196.
Memory consumption went from 249.8 MB to 249.1 MB.
Total: 15.000200 ms (FindLiveObjects: 0.983700 ms CreateObjectMapping: 0.474900 ms MarkObjects: 12.185300 ms  DeleteObjects: 1.354200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014608 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.412 seconds
Domain Reload Profiling:
	ReloadAssembly (2413ms)
		BeginReloadAssembly (238ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (2014ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (352ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1390ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1172ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4211.
Memory consumption went from 249.8 MB to 249.1 MB.
Total: 9.514200 ms (FindLiveObjects: 0.799600 ms CreateObjectMapping: 0.493500 ms MarkObjects: 7.457000 ms  DeleteObjects: 0.762700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014186 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.398 seconds
Domain Reload Profiling:
	ReloadAssembly (2399ms)
		BeginReloadAssembly (290ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (81ms)
		EndReloadAssembly (1953ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (346ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1339ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1124ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4226.
Memory consumption went from 249.8 MB to 249.1 MB.
Total: 11.152700 ms (FindLiveObjects: 1.067100 ms CreateObjectMapping: 0.408800 ms MarkObjects: 8.311300 ms  DeleteObjects: 1.363600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 19.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (0.7 MB). Loaded Objects now: 4226.
Memory consumption went from 174.8 MB to 174.0 MB.
Total: 12.768600 ms (FindLiveObjects: 1.047000 ms CreateObjectMapping: 0.323800 ms MarkObjects: 9.731800 ms  DeleteObjects: 1.664600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015713 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  9.812 seconds
Domain Reload Profiling:
	ReloadAssembly (9814ms)
		BeginReloadAssembly (519ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (19ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (161ms)
		EndReloadAssembly (9118ms)
			LoadAssemblies (237ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (393ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1457ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1218ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4241.
Memory consumption went from 249.9 MB to 249.2 MB.
Total: 10.233000 ms (FindLiveObjects: 0.472700 ms CreateObjectMapping: 0.263300 ms MarkObjects: 8.649300 ms  DeleteObjects: 0.846100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3682.518915 seconds.
  path: Assets/Game scene/Farming mod.unity
  artifactKey: Guid(ebb0204f92bb3ab47bfb1a7825f65c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/Farming mod.unity using Guid(ebb0204f92bb3ab47bfb1a7825f65c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '209b91c75237d0512358cf3ff7f81472') in 0.121397 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015163 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.389 seconds
Domain Reload Profiling:
	ReloadAssembly (2391ms)
		BeginReloadAssembly (255ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (1978ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (353ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1337ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1118ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4256.
Memory consumption went from 249.9 MB to 249.2 MB.
Total: 7.965200 ms (FindLiveObjects: 0.557800 ms CreateObjectMapping: 0.262200 ms MarkObjects: 6.296300 ms  DeleteObjects: 0.847800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.032807 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.484 seconds
Domain Reload Profiling:
	ReloadAssembly (2485ms)
		BeginReloadAssembly (251ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (2065ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (359ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1420ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (38ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1175ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4271.
Memory consumption went from 249.9 MB to 249.2 MB.
Total: 10.143600 ms (FindLiveObjects: 0.734100 ms CreateObjectMapping: 0.480000 ms MarkObjects: 7.546700 ms  DeleteObjects: 1.380800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014462 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.447 seconds
Domain Reload Profiling:
	ReloadAssembly (2448ms)
		BeginReloadAssembly (256ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2037ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (354ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (84ms)
			SetupLoadedEditorAssemblies (1404ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1181ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4286.
Memory consumption went from 249.9 MB to 249.2 MB.
Total: 7.605100 ms (FindLiveObjects: 0.446200 ms CreateObjectMapping: 0.252600 ms MarkObjects: 6.144100 ms  DeleteObjects: 0.761100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014680 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.394 seconds
Domain Reload Profiling:
	ReloadAssembly (2395ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (1990ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (351ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1369ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1149ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4301.
Memory consumption went from 249.9 MB to 249.2 MB.
Total: 7.736200 ms (FindLiveObjects: 0.455300 ms CreateObjectMapping: 0.254600 ms MarkObjects: 6.247000 ms  DeleteObjects: 0.778300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014668 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.458 seconds
Domain Reload Profiling:
	ReloadAssembly (2460ms)
		BeginReloadAssembly (277ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (1996ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (349ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1365ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1151ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4316.
Memory consumption went from 249.9 MB to 249.3 MB.
Total: 8.465000 ms (FindLiveObjects: 0.543200 ms CreateObjectMapping: 0.275900 ms MarkObjects: 6.699000 ms  DeleteObjects: 0.945600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015113 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.430 seconds
Domain Reload Profiling:
	ReloadAssembly (2431ms)
		BeginReloadAssembly (275ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (1976ms)
			LoadAssemblies (184ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (357ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1347ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1129ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4331.
Memory consumption went from 250.0 MB to 249.3 MB.
Total: 7.959900 ms (FindLiveObjects: 0.476100 ms CreateObjectMapping: 0.261500 ms MarkObjects: 6.346300 ms  DeleteObjects: 0.874700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014434 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.411 seconds
Domain Reload Profiling:
	ReloadAssembly (2412ms)
		BeginReloadAssembly (272ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (1982ms)
			LoadAssemblies (184ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (351ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1366ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1152ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4346.
Memory consumption went from 250.0 MB to 249.3 MB.
Total: 9.065100 ms (FindLiveObjects: 0.620600 ms CreateObjectMapping: 0.305200 ms MarkObjects: 7.316600 ms  DeleteObjects: 0.821500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014357 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.504 seconds
Domain Reload Profiling:
	ReloadAssembly (2505ms)
		BeginReloadAssembly (279ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2048ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (354ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1427ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1207ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4361.
Memory consumption went from 250.0 MB to 249.3 MB.
Total: 14.460500 ms (FindLiveObjects: 0.752600 ms CreateObjectMapping: 0.478200 ms MarkObjects: 11.831600 ms  DeleteObjects: 1.396200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016414 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.420 seconds
Domain Reload Profiling:
	ReloadAssembly (2421ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2013ms)
			LoadAssemblies (173ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (355ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (84ms)
			SetupLoadedEditorAssemblies (1370ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1143ms)
				ProcessInitializeOnLoadMethodAttributes (97ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4376.
Memory consumption went from 250.0 MB to 249.3 MB.
Total: 6.336600 ms (FindLiveObjects: 0.459300 ms CreateObjectMapping: 0.251300 ms MarkObjects: 4.881200 ms  DeleteObjects: 0.743400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014448 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.403 seconds
Domain Reload Profiling:
	ReloadAssembly (2404ms)
		BeginReloadAssembly (235ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (2008ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (346ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (1386ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1165ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4391.
Memory consumption went from 250.0 MB to 249.3 MB.
Total: 7.712700 ms (FindLiveObjects: 0.452600 ms CreateObjectMapping: 0.253800 ms MarkObjects: 6.235300 ms  DeleteObjects: 0.769900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1688.126714 seconds.
  path: Assets/Game scene/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '06fd0d89ab8de76b14a001b6c1e886dd') in 0.033069 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014611 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.486 seconds
Domain Reload Profiling:
	ReloadAssembly (2487ms)
		BeginReloadAssembly (274ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2033ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (389ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1340ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1118ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3691 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4406.
Memory consumption went from 250.1 MB to 249.4 MB.
Total: 8.182500 ms (FindLiveObjects: 0.534800 ms CreateObjectMapping: 0.289200 ms MarkObjects: 6.526800 ms  DeleteObjects: 0.830400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 69.723599 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_comment.png
  artifactKey: Guid(622fdb780ae13e840938631c5ed708f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_comment.png using Guid(622fdb780ae13e840938631c5ed708f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '51e1724898160b1c47b2ed69cda3bb2d') in 0.118733 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Demo scene/Lightmap-0_comp_dir.png
  artifactKey: Guid(4a3abcea8684f464a9b302dd7a047387) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Demo scene/Lightmap-0_comp_dir.png using Guid(4a3abcea8684f464a9b302dd7a047387) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '074109308390e22a26b87443243cd228') in 0.092133 seconds 
Number of asset objects unloaded after import = 2
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0