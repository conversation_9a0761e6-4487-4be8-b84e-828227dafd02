using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    public GameObject[] level;
    public Transform[] Position;
    public GameObject Tractor;
    void Start()
    {
        InitializeLevels();
        level[MainMenu.levlno].SetActive(true);
    }

    void InitializeLevels()
    {

        Tractor.transform.position = Position[MainMenu.levlno].position;
        Tractor.transform.rotation = Position[MainMenu.levlno].rotation;

    }

    public void NextLevel()
    {
        
        MainMenu.CompleteFarmLevel(MainMenu.levlno);

        if (MainMenu.levlno < level.Length - 1)
        {
            
            MainMenu.levlno++;

            PlayerPrefs.SetInt("Farm" + MainMenu.levlno, 1);
            PlayerPrefs.Save();

            SceneManager.LoadScene("Farming mod");
        }
        else
        {
            
            SceneManager.LoadScene("MAINMENU");
        }
    }

}
