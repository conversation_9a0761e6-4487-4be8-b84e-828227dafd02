/*
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 * ███████╗ █████╗ ██████╗ ███╗   ███╗██╗███╗   ██╗ ██████╗     ███╗   ███╗ ██████╗ ██████╗     ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗
 * ██╔════╝██╔══██╗██╔══██╗████╗ ████║██║████╗  ██║██╔════╝     ████╗ ████║██╔═══██╗██╔══██╗    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
 * █████╗  ███████║██████╔╝██╔████╔██║██║██╔██╗ ██║██║  ███╗    ██╔████╔██║██║   ██║██║  ██║    ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
 * ██╔══╝  ██╔══██║██╔══██╗██║╚██╔╝██║██║██║╚██╗██║██║   ██║    ██║╚██╔╝██║██║   ██║██║  ██║    ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
 * ██║     ██║  ██║██║  ██║██║ ╚═╝ ██║██║██║ ╚████║╚██████╔╝    ██║ ╚═╝ ██║╚██████╔╝██████╔╝    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
 * ╚═╝     ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝╚═╝╚═╝  ╚═══╝ ╚═════╝     ╚═╝     ╚═╝ ╚═════╝ ╚═════╝     ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * 🚜 TRACTOR SIMULATOR CARGO GAMES - FARMING MODE MANAGER 🌾
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📋 SCRIPT INFORMATION                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  📁 File Name      : FarmingModmanager.cs                                                                          │
 * │  🎯 Purpose        : Farming Mode Level Management & Progression System                                            │
 * │  🏗️  Architecture   : Clean Component-Based Design with Organized Structure                                        │
 * │  🎨 Script Organizer: Ali Taj                                                                                       │
 * │  📅 Organized Date : 2025-07-30                                                                                     │
 * │  ⚡ Version        : V1.9 - Optimized & Beautifully Organized                                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  🌟 KEY FEATURES                                                                                                    │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🌾 Farming Level System      - Dynamic farming level loading and management                                       │
 * │  🚜 Tractor Positioning       - Automatic tractor placement for each level                                         │
 * │  💾 Progress Tracking         - PlayerPrefs integration for farm level completion                                  │
 * │  🎯 Level Progression         - Seamless advancement through farming challenges                                     │
 * │  🔄 Scene Management          - Smooth transitions between farming levels                                           │
 * │  ✨ Clean Architecture        - Well-organized, maintainable code structure                                        │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📚 ORGANIZATION STRUCTURE                                                                                          │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🔹 Level Configuration       - Level arrays and positioning systems                                               │
 * │  🔹 Unity Lifecycle          - Start method with proper initialization                                             │
 * │  🔹 Initialization Methods   - Level setup and tractor positioning                                                 │
 * │  🔹 Level Progression        - Next level functionality with progress saving                                       │
 * │  🔹 Scene Management         - Smooth scene transitions and loading                                                │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  💡 USAGE INSTRUCTIONS                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  1️⃣  Attach this script to a GameObject in your Farming Mode scene                                               │
 * │  2️⃣  Configure level array with all farming level GameObjects                                                     │
 * │  3️⃣  Set up Position array with spawn points for each level                                                       │
 * │  4️⃣  Assign the Tractor GameObject reference                                                                      │
 * │  5️⃣  Call NextLevel() when player completes current farming challenge                                             │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎨 Beautifully Organized by Ali Taj - Making Code Art! ✨
 *
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    public GameObject[] level;
    public Transform[] Position;
    public GameObject Tractor;

    void Start()
    {
        InitializeLevels();
        level[MainMenu.levlno].SetActive(true);
    }

    void InitializeLevels()
    {
        Tractor.transform.position = Position[MainMenu.levlno].position;
        Tractor.transform.rotation = Position[MainMenu.levlno].rotation;
    }

    public void NextLevel()
    {
        MainMenu.CompleteFarmLevel(MainMenu.levlno);

        if (MainMenu.levlno < level.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Farm" + MainMenu.levlno, 1);
            PlayerPrefs.Save();
            SceneManager.LoadScene("Farming mod");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }
}
