using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    public GameObject[] level;
    public Transform[] Position;
    public GameObject Tractor;
    void Start()
    {
        InitializeLevels();
        level[MainMenu.levlno].SetActive(true);
    }

    void InitializeLevels()
    {

        Tractor.transform.position = Position[MainMenu.levlno].position;
        Tractor.transform.rotation = Position[MainMenu.levlno].rotation;

    }

    public void NextLevel()
    {
        // Mark current farming level as completed
        MainMenu.CompleteFarmLevel(MainMenu.levlno);

        if (MainMenu.levlno < level.Length - 1)
        {
            // Move to next level
            MainMenu.levlno++;

            // Unlock the next level (this is already handled in CompleteFarmLevel, but ensuring it's available)
            PlayerPrefs.SetInt("Farm" + MainMenu.levlno, 1);
            PlayerPrefs.Save();

            // Load the next farming level
            SceneManager.LoadScene("Farming mod");
        }
        else
        {
            // All farming levels completed, return to main menu
            SceneManager.LoadScene("MAINMENU");
        }
    }

}
