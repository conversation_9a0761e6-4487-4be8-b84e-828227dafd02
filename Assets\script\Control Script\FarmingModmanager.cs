/*
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 * ███████╗ █████╗ ██████╗ ███╗   ███╗██╗███╗   ██╗ ██████╗     ███╗   ███╗ ██████╗ ██████╗     ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗
 * ██╔════╝██╔══██╗██╔══██╗████╗ ████║██║████╗  ██║██╔════╝     ████╗ ████║██╔═══██╗██╔══██╗    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
 * █████╗  ███████║██████╔╝██╔████╔██║██║██╔██╗ ██║██║  ███╗    ██╔████╔██║██║   ██║██║  ██║    ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
 * ██╔══╝  ██╔══██║██╔══██╗██║╚██╔╝██║██║██║╚██╗██║██║   ██║    ██║╚██╔╝██║██║   ██║██║  ██║    ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
 * ██║     ██║  ██║██║  ██║██║ ╚═╝ ██║██║██║ ╚████║╚██████╔╝    ██║ ╚═╝ ██║╚██████╔╝██████╔╝    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
 * ╚═╝     ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝╚═╝╚═╝  ╚═══╝ ╚═════╝     ╚═╝     ╚═╝ ╚═════╝ ╚═════╝     ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 *
 * 🚜 TRACTOR SIMULATOR CARGO GAMES - FARMING MODE MANAGER 🌾
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📋 SCRIPT INFORMATION                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  📁 File Name      : FarmingModmanager.cs                                                                          │
 * │  🎯 Purpose        : Farming Mode Level Management & Progression System                                            │
 * │  🏗️  Architecture   : Clean Component-Based Design with Organized Structure                                        │
 * │  🎨 Script Organizer: Ali Taj                                                                                       │
 * │  📅 Organized Date : 2025-07-30                                                                                     │
 * │  ⚡ Version        : V1.9 - Optimized & Beautifully Organized                                                      │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  🌟 KEY FEATURES                                                                                                    │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🌾 Farming Level System      - Dynamic farming level loading and management                                       │
 * │  🚜 Tractor Positioning       - Automatic tractor placement for each level                                         │
 * │  💾 Progress Tracking         - PlayerPrefs integration for farm level completion                                  │
 * │  🎯 Level Progression         - Seamless advancement through farming challenges                                     │
 * │  🔄 Scene Management          - Smooth transitions between farming levels                                           │
 * │  ✨ Clean Architecture        - Well-organized, maintainable code structure                                        │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  📚 ORGANIZATION STRUCTURE                                                                                          │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  🔹 Level Configuration       - Level arrays and positioning systems                                               │
 * │  🔹 Unity Lifecycle          - Start method with proper initialization                                             │
 * │  🔹 Initialization Methods   - Level setup and tractor positioning                                                 │
 * │  🔹 Level Progression        - Next level functionality with progress saving                                       │
 * │  🔹 Scene Management         - Smooth scene transitions and loading                                                │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
 * │  💡 USAGE INSTRUCTIONS                                                                                              │
 * ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
 * │  1️⃣  Attach this script to a GameObject in your Farming Mode scene                                               │
 * │  2️⃣  Configure level array with all farming level GameObjects                                                     │
 * │  3️⃣  Set up Position array with spawn points for each level                                                       │
 * │  4️⃣  Assign the Tractor GameObject reference                                                                      │
 * │  5️⃣  Call NextLevel() when player completes current farming challenge                                             │
 * └─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
 *
 * 🎨 Beautifully Organized by Ali Taj - Making Code Art! ✨
 *
 * ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    #region Level Configuration
    [Header("Farming Level Setup")]
    [Tooltip("Array of all farming level GameObjects")]
    public GameObject[] level;

    [Header("Tractor Configuration")]
    [Tooltip("Spawn positions for tractor in each level")]
    public Transform[] Position;

    [Tooltip("Main tractor GameObject to be positioned")]
    public GameObject Tractor;
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Initialize the farming level system on start
    /// </summary>
    void Start()
    {
        InitializeFarmingLevel();
        ActivateCurrentLevel();
    }
    #endregion

    #region Initialization Methods
    /// <summary>
    /// Initialize the current farming level with proper tractor positioning
    /// </summary>
    private void InitializeFarmingLevel()
    {
        SetupTractorPosition();
    }

    /// <summary>
    /// Position the tractor at the appropriate spawn point for current level
    /// </summary>
    private void SetupTractorPosition()
    {
        if (Position != null && Position.Length > MainMenu.levlno && Position[MainMenu.levlno] != null)
        {
            Tractor.transform.position = Position[MainMenu.levlno].position;
            Tractor.transform.rotation = Position[MainMenu.levlno].rotation;
        }
    }

    /// <summary>
    /// Activate the current level GameObject
    /// </summary>
    private void ActivateCurrentLevel()
    {
        if (level != null && level.Length > MainMenu.levlno && level[MainMenu.levlno] != null)
        {
            level[MainMenu.levlno].SetActive(true);
        }
    }
    #endregion

    #region Level Progression
    /// <summary>
    /// Progress to the next farming level or return to main menu if all levels completed
    /// </summary>
    public void NextLevel()
    {
        CompleteFarmingLevel();

        if (HasMoreLevels())
        {
            AdvanceToNextLevel();
        }
        else
        {
            ReturnToMainMenu();
        }
    }

    /// <summary>
    /// Mark current farming level as completed and save progress
    /// </summary>
    private void CompleteFarmingLevel()
    {
        MainMenu.CompleteFarmLevel(MainMenu.levlno);
    }

    /// <summary>
    /// Check if there are more farming levels available
    /// </summary>
    /// <returns>True if more levels exist, false otherwise</returns>
    private bool HasMoreLevels()
    {
        return MainMenu.levlno < level.Length - 1;
    }

    /// <summary>
    /// Advance to the next farming level
    /// </summary>
    private void AdvanceToNextLevel()
    {
        MainMenu.levlno++;
        UnlockNextLevel();
        LoadFarmingScene();
    }

    /// <summary>
    /// Unlock the next farming level in PlayerPrefs
    /// </summary>
    private void UnlockNextLevel()
    {
        PlayerPrefs.SetInt("Farm" + MainMenu.levlno, 1);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Load the farming mode scene for the next level
    /// </summary>
    private void LoadFarmingScene()
    {
        SceneManager.LoadScene("Farming mod");
    }

    /// <summary>
    /// Return to main menu when all farming levels are completed
    /// </summary>
    private void ReturnToMainMenu()
    {
        SceneManager.LoadScene("MAINMENU");
    }
    #endregion
}
